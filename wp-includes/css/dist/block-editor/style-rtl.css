:root{
  --wp-admin-theme-color:#007cba;
  --wp-admin-theme-color--rgb:0, 124, 186;
  --wp-admin-theme-color-darker-10:#006ba1;
  --wp-admin-theme-color-darker-10--rgb:0, 107, 161;
  --wp-admin-theme-color-darker-20:#005a87;
  --wp-admin-theme-color-darker-20--rgb:0, 90, 135;
  --wp-admin-border-width-focus:2px;
  --wp-block-synced-color:#7a00df;
  --wp-block-synced-color--rgb:122, 0, 223;
  --wp-bound-block-color:var(--wp-block-synced-color);
}
@media (min-resolution:192dpi){
  :root{
    --wp-admin-border-width-focus:1.5px;
  }
}

.block-editor-autocompleters__block{
  white-space:nowrap;
}
.block-editor-autocompleters__block .block-editor-block-icon{
  margin-left:8px;
}
.block-editor-autocompleters__block[aria-selected=true] .block-editor-block-icon{
  color:inherit !important;
}

.block-editor-autocompleters__link{
  white-space:nowrap;
}
.block-editor-autocompleters__link .block-editor-block-icon{
  margin-left:8px;
}

.block-editor-global-styles-background-panel__inspector-media-replace-container{
  border:1px solid #ddd;
  border-radius:2px;
  grid-column:1 /  -1;
}
.block-editor-global-styles-background-panel__inspector-media-replace-container.is-open{
  background-color:#f0f0f0;
}
.block-editor-global-styles-background-panel__inspector-media-replace-container .block-editor-global-styles-background-panel__image-tools-panel-item{
  border:0;
  flex-grow:1;
}
.block-editor-global-styles-background-panel__inspector-media-replace-container .block-editor-global-styles-background-panel__image-tools-panel-item .components-dropdown{
  display:block;
}
.block-editor-global-styles-background-panel__inspector-media-replace-container .block-editor-global-styles-background-panel__inspector-preview-inner{
  height:100%;
}
.block-editor-global-styles-background-panel__inspector-media-replace-container .components-dropdown{
  display:block;
  height:36px;
}

.block-editor-global-styles-background-panel__image-tools-panel-item{
  border:1px solid #ddd;
  grid-column:1 /  -1;
  position:relative;
}
.block-editor-global-styles-background-panel__image-tools-panel-item .components-drop-zone__content-icon{
  display:none;
}
.block-editor-global-styles-background-panel__image-tools-panel-item .components-dropdown{
  display:block;
  height:36px;
}
.block-editor-global-styles-background-panel__image-tools-panel-item button.components-button{
  color:#1e1e1e;
  display:block;
  width:100%;
}
.block-editor-global-styles-background-panel__image-tools-panel-item button.components-button:hover{
  color:var(--wp-admin-theme-color);
}
.block-editor-global-styles-background-panel__image-tools-panel-item button.components-button:focus{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.block-editor-global-styles-background-panel__image-tools-panel-item .block-editor-global-styles-background-panel__loading{
  height:100%;
  padding:10px 0 0;
  position:absolute;
  width:100%;
  z-index:1;
}
.block-editor-global-styles-background-panel__image-tools-panel-item .block-editor-global-styles-background-panel__loading svg{
  margin:0;
}

.block-editor-global-styles-background-panel__dropdown-toggle,.block-editor-global-styles-background-panel__image-preview-content{
  height:100%;
  padding-right:12px;
  width:100%;
}

.block-editor-global-styles-background-panel__dropdown-toggle{
  background:#0000;
  border:none;
  cursor:pointer;
}

.block-editor-global-styles-background-panel__inspector-media-replace-title{
  text-align:start;
  text-align-last:center;
  white-space:normal;
  word-break:break-all;
}

.block-editor-global-styles-background-panel__inspector-preview-inner .block-editor-global-styles-background-panel__inspector-image-indicator-wrapper{
  height:20px;
  min-width:auto;
  width:20px;
}

.block-editor-global-styles-background-panel__inspector-image-indicator{
  background-size:cover;
  border-radius:50%;
  display:block;
  height:20px;
  position:relative;
  width:20px;
}

.block-editor-global-styles-background-panel__inspector-image-indicator:after{
  border:1px solid #0000;
  border-radius:50%;
  bottom:-1px;
  box-shadow:inset 0 0 0 1px #0003;
  box-sizing:inherit;
  content:"";
  left:-1px;
  position:absolute;
  right:-1px;
  top:-1px;
}

.block-editor-global-styles-background-panel__dropdown-content-wrapper{
  min-width:260px;
  overflow-x:hidden;
}
.block-editor-global-styles-background-panel__dropdown-content-wrapper .components-focal-point-picker-wrapper{
  background-color:#f0f0f0;
  border:1px solid #ddd;
  border-radius:2px;
  width:100%;
}
.block-editor-global-styles-background-panel__dropdown-content-wrapper .components-focal-point-picker__media--image{
  max-height:180px;
}
.block-editor-global-styles-background-panel__dropdown-content-wrapper .components-focal-point-picker:after{
  content:none;
}

.modal-open .block-editor-global-styles-background-panel__popover{
  z-index:159890;
}

.block-editor-global-styles-background-panel__media-replace-popover .components-popover__content{
  width:226px;
}
.block-editor-global-styles-background-panel__media-replace-popover .components-button{
  padding:0 8px;
}
.block-editor-global-styles-background-panel__media-replace-popover .components-button .components-menu-items__item-icon.has-icon-right{
  margin-right:16px;
}

.block-editor-block-alignment-control__menu-group .components-menu-item__info{
  margin-top:0;
}

iframe[name=editor-canvas]{
  background-color:initial;
  box-sizing:border-box;
  display:block;
  height:100%;
  transition:all .4s cubic-bezier(.46, .03, .52, .96);
  width:100%;
}
@media (prefers-reduced-motion:reduce){
  iframe[name=editor-canvas]{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-block-icon{
  align-items:center;
  display:flex;
  height:24px;
  justify-content:center;
  width:24px;
}
.block-editor-block-icon.has-colors svg{
  fill:currentColor;
}
@media (forced-colors:active){
  .block-editor-block-icon.has-colors svg{
    fill:CanvasText;
  }
}
.block-editor-block-icon svg{
  max-height:24px;
  max-width:24px;
  min-height:20px;
  min-width:20px;
}

.block-editor-block-inspector p:not(.components-base-control__help){
  margin-top:0;
}
.block-editor-block-inspector h2,.block-editor-block-inspector h3{
  color:#1e1e1e;
  font-size:13px;
  margin-bottom:1.5em;
}
.block-editor-block-inspector .components-base-control:where(:not(:last-child)),.block-editor-block-inspector .components-radio-control:where(:not(:last-child)){
  margin-bottom:16px;
}
.block-editor-block-inspector .components-focal-point-picker-control .components-base-control,.block-editor-block-inspector .components-query-controls .components-base-control,.block-editor-block-inspector .components-range-control .components-base-control{
  margin-bottom:0;
}
.block-editor-block-inspector .components-panel__body{
  border:none;
  border-top:1px solid #e0e0e0;
  margin-top:-1px;
}

.block-editor-block-inspector__no-block-tools,.block-editor-block-inspector__no-blocks{
  background:#fff;
  display:block;
  font-size:13px;
  padding:32px 16px;
  text-align:center;
}

.block-editor-block-inspector__no-block-tools{
  border-top:1px solid #ddd;
}

.block-editor-block-inspector__tab-item{
  display:flex;
  flex:1 1 0px;
  justify-content:center;
}
.block-editor-block-list__insertion-point{
  bottom:0;
  left:0;
  position:absolute;
  right:0;
  top:0;
}

.block-editor-block-list__insertion-point-indicator{
  background:var(--wp-admin-theme-color);
  border-radius:2px;
  opacity:0;
  position:absolute;
  transform-origin:center;
  will-change:transform, opacity;
}
.block-editor-block-list__insertion-point.is-vertical>.block-editor-block-list__insertion-point-indicator{
  height:4px;
  top:calc(50% - 2px);
  width:100%;
}
.block-editor-block-list__insertion-point.is-horizontal>.block-editor-block-list__insertion-point-indicator{
  bottom:0;
  right:calc(50% - 2px);
  top:0;
  width:4px;
}

.block-editor-block-list__insertion-point-inserter{
  display:none;
  justify-content:center;
  position:absolute;
  right:calc(50% - 12px);
  top:calc(50% - 12px);
  will-change:transform;
}
@media (min-width:480px){
  .block-editor-block-list__insertion-point-inserter{
    display:flex;
  }
}

.block-editor-block-list__block-side-inserter-popover .components-popover__content>div{
  pointer-events:none;
}
.block-editor-block-list__block-side-inserter-popover .components-popover__content>div>*{
  pointer-events:all;
}

.block-editor-block-list__empty-block-inserter .block-editor-inserter__toggle.components-button.has-icon,.block-editor-block-list__insertion-point-inserter .block-editor-inserter__toggle.components-button.has-icon{
  background:#1e1e1e;
  color:#fff;
  height:24px;
  min-width:24px;
  padding:0;
}
.block-editor-block-list__empty-block-inserter .block-editor-inserter__toggle.components-button.has-icon:hover,.block-editor-block-list__insertion-point-inserter .block-editor-inserter__toggle.components-button.has-icon:hover{
  background:var(--wp-admin-theme-color);
  color:#fff;
}

.block-editor-block-list__insertion-point-inserter .block-editor-inserter__toggle.components-button.has-icon{
  background:var(--wp-admin-theme-color);
}
.block-editor-block-list__insertion-point-inserter .block-editor-inserter__toggle.components-button.has-icon:hover{
  background:#1e1e1e;
}
.block-editor-block-list__block-selection-button{
  background-color:#1e1e1e;
  border-radius:2px;
  display:inline-flex;
  font-size:13px;
  height:48px;
  padding:0 12px;
  z-index:22;
}
.block-editor-block-list__block-selection-button .block-editor-block-list__block-selection-button__content{
  align-items:center;
  display:inline-flex;
  margin:auto;
}
.block-editor-block-list__block-selection-button .block-editor-block-list__block-selection-button__content>.components-flex__item{
  margin-left:6px;
}
.block-editor-block-list__block-selection-button .components-button.has-icon.block-selection-button_drag-handle{
  cursor:grab;
  height:24px;
  margin-right:-2px;
  min-width:24px;
  padding:0;
}
.block-editor-block-list__block-selection-button .components-button.has-icon.block-selection-button_drag-handle svg{
  min-height:18px;
  min-width:18px;
}
.block-editor-block-list__block-selection-button .block-editor-block-icon{
  color:#fff;
  font-size:13px;
  height:48px;
}
.block-editor-block-list__block-selection-button .components-button{
  color:#fff;
  display:flex;
  height:48px;
  min-width:36px;
}
.block-editor-block-list__block-selection-button .components-button:focus{
  border:none;
  box-shadow:none;
}
.block-editor-block-list__block-selection-button .components-button:active,.block-editor-block-list__block-selection-button .components-button[aria-disabled=true]:hover{
  color:#fff;
}
.block-editor-block-list__block-selection-button .block-selection-button_select-button.components-button{
  padding:0;
}
.block-editor-block-list__block-selection-button .block-editor-block-mover{
  background:unset;
  border:none;
}

@keyframes hide-during-dragging{
  to{
    position:fixed;
    transform:translate(-9999px, 9999px);
  }
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-contextual-toolbar,.components-popover.block-editor-block-list__block-popover .block-editor-block-list__block-selection-button{
  margin-bottom:8px;
  margin-top:8px;
  pointer-events:all;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-contextual-toolbar{
  border:1px solid #1e1e1e;
  border-radius:2px;
  overflow:visible;
  position:static;
  width:auto;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-contextual-toolbar.has-parent{
  margin-right:56px;
}
.show-icon-labels .components-popover.block-editor-block-list__block-popover .block-editor-block-contextual-toolbar.has-parent{
  margin-right:0;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-toolbar{
  overflow:visible;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-toolbar .components-toolbar,.components-popover.block-editor-block-list__block-popover .block-editor-block-toolbar .components-toolbar-group{
  border-left-color:#1e1e1e;
}
.components-popover.block-editor-block-list__block-popover.is-insertion-point-visible{
  visibility:hidden;
}
.is-dragging-components-draggable .components-popover.block-editor-block-list__block-popover{
  animation:hide-during-dragging 1ms linear forwards;
  opacity:0;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-parent-selector{
  position:absolute;
  right:-57px;
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-parent-selector:before{
  content:"";
}
.components-popover.block-editor-block-list__block-popover .block-editor-block-parent-selector .block-editor-block-parent-selector__button{
  background-color:#fff;
  border:1px solid #1e1e1e;
  padding-left:6px;
  padding-right:6px;
}
.show-icon-labels .components-popover.block-editor-block-list__block-popover .block-editor-block-parent-selector .block-editor-block-parent-selector__button{
  padding-left:12px;
  padding-right:12px;
}
.show-icon-labels .components-popover.block-editor-block-list__block-popover .block-editor-block-parent-selector{
  margin-right:-1px;
  position:relative;
  right:auto;
}
.show-icon-labels .components-popover.block-editor-block-list__block-popover .block-editor-block-mover__move-button-container,.show-icon-labels .components-popover.block-editor-block-list__block-popover .block-editor-block-toolbar__block-controls .block-editor-block-mover{
  border-right:1px solid #1e1e1e;
}

.is-dragging-components-draggable .components-tooltip{
  display:none;
}

.components-popover.block-editor-block-popover__inbetween .block-editor-button-pattern-inserter__button{
  pointer-events:all;
  position:absolute;
  right:50%;
  top:50%;
  transform:translateX(50%) translateY(-50%);
}

.components-button.block-editor-button-pattern-inserter__button.block-editor-block-tools__zoom-out-mode-inserter-button{
  top:-1px;
}

.block-editor-block-lock-modal{
  z-index:1000001;
}
@media (min-width:600px){
  .block-editor-block-lock-modal .components-modal__frame{
    max-width:480px;
  }
}

.block-editor-block-lock-modal__options legend{
  margin-bottom:16px;
  padding:0;
}

.block-editor-block-lock-modal__checklist{
  margin:0;
}

.block-editor-block-lock-modal__options-all{
  padding:12px 0;
}
.block-editor-block-lock-modal__options-all .components-checkbox-control__label{
  font-weight:600;
}

.block-editor-block-lock-modal__checklist-item{
  align-items:center;
  display:flex;
  gap:12px;
  justify-content:space-between;
  margin-bottom:0;
  padding:12px 32px 12px 0;
}
.block-editor-block-lock-modal__checklist-item .block-editor-block-lock-modal__lock-icon{
  flex-shrink:0;
  margin-left:12px;
  fill:#1e1e1e;
}
.block-editor-block-lock-modal__checklist-item:hover{
  background-color:#f0f0f0;
  border-radius:2px;
}

.block-editor-block-lock-modal__template-lock{
  border-top:1px solid #ddd;
  margin-top:16px;
  padding-top:16px;
}

.block-editor-block-lock-modal__actions{
  margin-top:24px;
}

.block-editor-block-lock-toolbar .components-button.has-icon{
  min-width:36px !important;
}

.block-editor-block-toolbar__block-controls .block-editor-block-lock-toolbar{
  margin-right:-6px !important;
}

.show-icon-labels .block-editor-block-toolbar__block-controls .block-editor-block-lock-toolbar{
  border-right:1px solid #1e1e1e;
  margin-left:-6px;
  margin-right:6px !important;
}

.block-editor-block-breadcrumb{
  list-style:none;
  margin:0;
  padding:0;
}
.block-editor-block-breadcrumb li{
  display:inline-flex;
  margin:0;
}
.block-editor-block-breadcrumb li .block-editor-block-breadcrumb__separator{
  fill:currentColor;
  margin-left:-4px;
  margin-right:-4px;
  transform:scaleX(-1);;
}
.block-editor-block-breadcrumb li:last-child .block-editor-block-breadcrumb__separator{
  display:none;
}

.block-editor-block-breadcrumb__current{
  cursor:default;
}

.block-editor-block-breadcrumb__button.block-editor-block-breadcrumb__button,.block-editor-block-breadcrumb__current{
  color:#1e1e1e;
  font-size:inherit;
  padding:0 8px;
}

.block-editor-block-card{
  align-items:flex-start;
  color:#1e1e1e;
  display:flex;
  padding:16px;
}

.block-editor-block-card__title{
  font-weight:500;
}
.block-editor-block-card__title.block-editor-block-card__title{
  font-size:13px;
  line-height:1.4;
  margin:0;
  padding:3px 0;
}

.block-editor-block-card .block-editor-block-icon{
  flex:0 0 24px;
  height:24px;
  margin-left:12px;
  margin-right:0;
  width:24px;
}

.block-editor-block-card.is-synced .block-editor-block-icon{
  color:var(--wp-block-synced-color);
}
.block-editor-block-compare{
  height:auto;
}

.block-editor-block-compare__wrapper{
  display:flex;
  padding-bottom:16px;
}
.block-editor-block-compare__wrapper>div{
  display:flex;
  flex-direction:column;
  justify-content:space-between;
  max-width:600px;
  min-width:200px;
  padding:0 0 0 16px;
  width:50%;
}
.block-editor-block-compare__wrapper>div button{
  float:left;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__converted{
  border-right:1px solid #ddd;
  padding-left:0;
  padding-right:15px;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__html{
  border-bottom:1px solid #ddd;
  color:#1e1e1e;
  font-family:Menlo,Consolas,monaco,monospace;
  font-size:12px;
  line-height:1.7;
  padding-bottom:15px;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__html span{
  background-color:#e6ffed;
  padding-bottom:3px;
  padding-top:3px;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__html span.block-editor-block-compare__added{
  background-color:#acf2bd;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__html span.block-editor-block-compare__removed{
  background-color:#cc1818;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__preview{
  padding:16px 0 0;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__preview p{
  font-size:12px;
  margin-top:0;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__action{
  margin-top:16px;
}
.block-editor-block-compare__wrapper .block-editor-block-compare__heading{
  font-size:1em;
  font-weight:400;
  margin:.67em 0;
}

.block-editor-block-draggable-chip-wrapper{
  position:absolute;
  right:0;
  top:-24px;
}

.block-editor-block-draggable-chip{
  background-color:#1e1e1e;
  border-radius:2px;
  box-shadow:0 6px 8px #0000004d;
  color:#fff;
  cursor:grabbing;
  display:inline-flex;
  height:48px;
  padding:0 13px;
  position:relative;
  -webkit-user-select:none;
          user-select:none;
  width:max-content;
}
.block-editor-block-draggable-chip svg{
  fill:currentColor;
}
.block-editor-block-draggable-chip .block-editor-block-draggable-chip__content{
  justify-content:flex-start;
  margin:auto;
}
.block-editor-block-draggable-chip .block-editor-block-draggable-chip__content>.components-flex__item{
  margin-left:6px;
}
.block-editor-block-draggable-chip .block-editor-block-draggable-chip__content>.components-flex__item:last-child{
  margin-left:0;
}
.block-editor-block-draggable-chip .block-editor-block-draggable-chip__content .block-editor-block-icon svg{
  min-height:18px;
  min-width:18px;
}
.block-editor-block-draggable-chip .components-flex__item{
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
}

.block-editor-block-draggable-chip__disabled.block-editor-block-draggable-chip__disabled{
  align-items:center;
  background-color:initial;
  bottom:0;
  display:flex;
  justify-content:center;
  left:0;
  opacity:0;
  position:absolute;
  right:0;
  top:0;
  transition:all .1s linear .1s;
}
.block-editor-block-draggable-chip__disabled.block-editor-block-draggable-chip__disabled .block-editor-block-draggable-chip__disabled-icon{
  background:#0000 linear-gradient(45deg, #0000 47.5%, #fff 0, #fff 52.5%, #0000 0);
  border-radius:50%;
  box-shadow:inset 0 0 0 1.5px #fff;
  display:inline-block;
  height:20px;
  padding:0;
  width:20px;
}

.block-draggable-invalid-drag-token .block-editor-block-draggable-chip__disabled.block-editor-block-draggable-chip__disabled{
  background-color:#757575;
  box-shadow:0 4px 8px #0003;
  opacity:1;
}

.block-editor-block-mover__move-button-container{
  border:none;
  display:flex;
  justify-content:center;
  padding:0;
}
@media (min-width:600px){
  .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container{
    flex-direction:column;
  }
  .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container>*{
    height:20px;
    min-width:0 !important;
    width:100%;
  }
  .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container>:before{
    height:calc(100% - 4px);
  }
  .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container .block-editor-block-mover-button.is-up-button svg{
    flex-shrink:0;
    top:3px;
  }
  .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container .block-editor-block-mover-button.is-down-button svg{
    bottom:3px;
    flex-shrink:0;
  }
  .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container{
    width:48px;
  }
  .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container>*{
    min-width:0 !important;
    overflow:hidden;
    width:24px;
  }
  .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container .block-editor-block-mover-button{
    padding-left:0;
    padding-right:0;
  }
  .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container .block-editor-block-mover-button.is-up-button svg{
    right:5px;
  }
  .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container .block-editor-block-mover-button.is-down-button svg{
    left:5px;
  }
}

.block-editor-block-mover__drag-handle{
  cursor:grab;
}
@media (min-width:600px){
  .block-editor-block-mover__drag-handle{
    min-width:0 !important;
    overflow:hidden;
    width:24px;
  }
  .block-editor-block-mover .block-editor-block-mover__drag-handle.has-icon.has-icon{
    padding-left:0;
    padding-right:0;
  }
}

.components-button.block-editor-block-mover-button:before{
  animation:components-button__appear-animation .1s ease;
  animation-fill-mode:forwards;
  border-radius:2px;
  content:"";
  display:block;
  height:32px;
  left:8px;
  position:absolute;
  right:8px;
  z-index:-1;
}
@media (prefers-reduced-motion:reduce){
  .components-button.block-editor-block-mover-button:before{
    animation-delay:0s;
    animation-duration:1ms;
  }
}
.components-button.block-editor-block-mover-button:focus,.components-button.block-editor-block-mover-button:focus:before,.components-button.block-editor-block-mover-button:focus:enabled{
  box-shadow:none;
  outline:none;
}
.components-button.block-editor-block-mover-button:focus-visible:before{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}

.block-editor-block-navigation__container{
  min-width:280px;
}

.block-editor-block-navigation__label{
  color:#757575;
  font-size:11px;
  font-weight:500;
  margin:0 0 12px;
  text-transform:uppercase;
}

.block-editor-block-patterns-list__list-item{
  cursor:pointer;
  margin-bottom:16px;
  position:relative;
}
.block-editor-block-patterns-list__list-item.is-placeholder{
  min-height:100px;
}
.block-editor-block-patterns-list__list-item[draggable=true]{
  cursor:grab;
}

.block-editor-block-patterns-list__item{
  height:100%;
  scroll-margin-bottom:56px;
  scroll-margin-top:24px;
}
.block-editor-block-patterns-list__item .block-editor-block-patterns-list__item-title{
  flex-grow:1;
  font-size:12px;
  text-align:right;
}
.block-editor-block-patterns-list__item .block-editor-block-preview__container{
  align-items:center;
  border-radius:4px;
  display:flex;
  overflow:hidden;
}
.block-editor-block-patterns-list__item .block-editor-block-preview__container:after{
  border-radius:4px;
  outline:1px solid #0000001a;
  outline-offset:-1px;
}
.block-editor-block-patterns-list__item:hover:not(:focus) .block-editor-block-preview__container:after{
  outline-color:#0000004d;
}
.block-editor-block-patterns-list__item:focus .block-editor-block-preview__container:after{
  outline-color:var(--wp-admin-theme-color);
  outline-offset:calc(-1*var(--wp-admin-border-width-focus));
  outline-width:var(--wp-admin-border-width-focus);
  transition:outline .1s linear;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-patterns-list__item:focus .block-editor-block-preview__container:after{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-block-patterns-list__item .block-editor-patterns__pattern-details:not(:empty){
  align-items:center;
  margin-top:8px;
  padding-bottom:4px;
}
.block-editor-block-patterns-list__item .block-editor-patterns__pattern-icon-wrapper{
  height:24px;
  min-width:24px;
}
.block-editor-block-patterns-list__item .block-editor-patterns__pattern-icon-wrapper .block-editor-patterns__pattern-icon{
  fill:var(--wp-block-synced-color);
}

.block-editor-patterns__grid-pagination-wrapper .block-editor-patterns__grid-pagination{
  border-top:1px solid #2f2f2f;
  justify-content:center;
  padding:4px;
}
.block-editor-patterns__grid-pagination-wrapper .block-editor-patterns__grid-pagination .components-button.is-tertiary{
  height:32px;
  justify-content:center;
  width:auto;
}
.block-editor-patterns__grid-pagination-wrapper .block-editor-patterns__grid-pagination .components-button.is-tertiary:disabled{
  background:none;
  color:#949494;
}
.block-editor-patterns__grid-pagination-wrapper .block-editor-patterns__grid-pagination .components-button.is-tertiary:hover:not(:disabled){
  background-color:#757575;
  color:#fff;
}

.show-icon-labels .block-editor-patterns__grid-pagination .components-button{
  width:auto;
}
.show-icon-labels .block-editor-patterns__grid-pagination .components-button span{
  display:none;
}
.show-icon-labels .block-editor-patterns__grid-pagination .components-button:before{
  content:attr(aria-label);
}

.components-popover.block-editor-block-popover{
  margin:0 !important;
  pointer-events:none;
  position:absolute;
  z-index:31;
}
.components-popover.block-editor-block-popover .components-popover__content{
  margin:0 !important;
  min-width:auto;
  overflow-y:visible;
  width:max-content;
}
.components-popover.block-editor-block-popover:not(.block-editor-block-popover__inbetween,.block-editor-block-popover__drop-zone,.block-editor-block-list__block-side-inserter-popover) .components-popover__content *{
  pointer-events:all;
}
.components-popover.block-editor-block-popover__inbetween,.components-popover.block-editor-block-popover__inbetween *{
  pointer-events:none;
}
.components-popover.block-editor-block-popover__inbetween .is-with-inserter,.components-popover.block-editor-block-popover__inbetween .is-with-inserter *{
  pointer-events:all;
}

.components-popover.block-editor-block-popover__drop-zone *{
  pointer-events:none;
}
.components-popover.block-editor-block-popover__drop-zone .block-editor-block-popover__drop-zone-foreground{
  background-color:var(--wp-admin-theme-color);
  border-radius:2px;
  inset:0;
  position:absolute;
}

.block-editor-block-preview__container{
  overflow:hidden;
  position:relative;
  width:100%;
}
.block-editor-block-preview__container .block-editor-block-preview__content{
  margin:0;
  min-height:auto;
  overflow:visible;
  right:0;
  text-align:initial;
  top:0;
  transform-origin:top right;
  width:100%;
}
.block-editor-block-preview__container .block-editor-block-preview__content .block-editor-block-list__insertion-point,.block-editor-block-preview__container .block-editor-block-preview__content .block-list-appender{
  display:none;
}

.block-editor-block-preview__container:after{
  bottom:0;
  content:"";
  left:0;
  position:absolute;
  right:0;
  top:0;
  z-index:1;
}

.block-editor-block-rename-modal{
  z-index:1000001;
}

.block-editor-block-styles__preview-panel{
  display:none;
  z-index:90;
}
@media (min-width:782px){
  .block-editor-block-styles__preview-panel{
    display:block;
  }
}
.block-editor-block-styles__preview-panel .block-editor-block-icon{
  display:none;
}

.block-editor-block-styles__variants{
  display:flex;
  flex-wrap:wrap;
  gap:8px;
  justify-content:space-between;
}
.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item{
  box-shadow:inset 0 0 0 1px #ddd;
  color:#1e1e1e;
  display:inline-block;
  width:calc(50% - 4px);
}
.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item:hover{
  box-shadow:inset 0 0 0 1px #ddd;
  color:var(--wp-admin-theme-color);
}
.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item.is-active,.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item.is-active:hover{
  background-color:#1e1e1e;
  box-shadow:none;
}
.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item.is-active .block-editor-block-styles__item-text,.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item.is-active:hover .block-editor-block-styles__item-text{
  color:#fff;
}
.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item.is-active:focus,.block-editor-block-styles__variants button.components-button.block-editor-block-styles__item:focus{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.block-editor-block-styles__variants .block-editor-block-styles__item-text{
  text-align:start;
  text-align-last:center;
  white-space:normal;
  word-break:break-all;
}

.block-editor-block-styles__block-preview-container,.block-editor-block-styles__block-preview-container *{
  box-sizing:border-box !important;
}

.block-editor-block-switcher{
  position:relative;
}
.block-editor-block-switcher .components-button.components-dropdown-menu__toggle.has-icon.has-icon{
  min-width:36px;
}

.block-editor-block-switcher__no-switcher-icon,.block-editor-block-switcher__toggle{
  position:relative;
}

.components-button.block-editor-block-switcher__no-switcher-icon,.components-button.block-editor-block-switcher__toggle{
  display:block;
  height:48px;
  margin:0;
}
.components-button.block-editor-block-switcher__no-switcher-icon .block-editor-block-icon,.components-button.block-editor-block-switcher__toggle .block-editor-block-icon{
  margin:auto;
}

.block-editor-block-switcher__toggle-text{
  margin-right:8px;
}
.show-icon-labels .block-editor-block-switcher__toggle-text{
  display:none;
}

.components-button.block-editor-block-switcher__no-switcher-icon{
  display:flex;
}
.components-button.block-editor-block-switcher__no-switcher-icon .block-editor-block-icon{
  margin-left:auto;
  margin-right:auto;
  min-width:24px !important;
}
.components-button.block-editor-block-switcher__no-switcher-icon[aria-disabled=true],.components-button.block-editor-block-switcher__no-switcher-icon[aria-disabled=true]:hover{
  color:#1e1e1e;
}

.components-popover.block-editor-block-switcher__popover .components-popover__content{
  min-width:300px;
}

.block-editor-block-switcher__popover-preview-container{
  bottom:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:-1px;
  width:100%;
}

.block-editor-block-switcher__popover-preview{
  overflow:hidden;
}
.block-editor-block-switcher__popover-preview .components-popover__content{
  background:#fff;
  border:1px solid #1e1e1e;
  border-radius:4px;
  box-shadow:none;
  outline:none;
  overflow:auto;
  width:300px;
}
.block-editor-block-switcher__popover-preview .block-editor-block-switcher__preview{
  margin:16px 0;
  max-height:468px;
  overflow:hidden;
  padding:0 16px;
}
.block-editor-block-switcher__popover-preview .block-editor-block-switcher__preview.is-pattern-list-preview{
  overflow:unset;
}

.block-editor-block-switcher__preview-title{
  color:#757575;
  font-size:11px;
  font-weight:500;
  margin-bottom:12px;
  text-transform:uppercase;
}

.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__no-switcher-icon{
  min-width:36px;
}
.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__no-switcher-icon,.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__toggle{
  height:48px;
}
.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__no-switcher-icon .block-editor-block-icon,.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__no-switcher-icon .block-editor-block-switcher__transform,.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__toggle .block-editor-block-icon,.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__toggle .block-editor-block-switcher__transform{
  height:48px;
  width:48px;
}
.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__no-switcher-icon .block-editor-block-switcher__transform,.block-editor-block-contextual-toolbar .components-button.block-editor-block-switcher__toggle .block-editor-block-switcher__transform{
  padding:12px;
}

.block-editor-block-switcher__preview-patterns-container{
  padding-bottom:16px;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item{
  margin-top:16px;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-preview__container{
  cursor:pointer;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-switcher__preview-patterns-container-list__item{
  border:1px solid #0000;
  border-radius:2px;
  height:100%;
  position:relative;
  transition:all .05s ease-in-out;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-switcher__preview-patterns-container-list__item:focus,.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-switcher__preview-patterns-container-list__item:hover{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-switcher__preview-patterns-container-list__item:hover{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) #1e1e1e;
}
.block-editor-block-switcher__preview-patterns-container .block-editor-block-switcher__preview-patterns-container-list__list-item .block-editor-block-switcher__preview-patterns-container-list__item .block-editor-block-switcher__preview-patterns-container-list__item-title{
  cursor:pointer;
  font-size:12px;
  padding:4px;
  text-align:center;
}

.block-editor-block-switcher__no-transforms{
  color:#757575;
  margin:0;
  padding:6px 8px;
}

.block-editor-block-switcher__binding-indicator{
  display:block;
  padding:8px;
}

.block-editor-block-types-list>[role=presentation]{
  display:flex;
  flex-wrap:wrap;
  overflow:hidden;
}

.block-editor-block-pattern-setup{
  align-items:flex-start;
  border-radius:2px;
  display:flex;
  flex-direction:column;
  justify-content:center;
  width:100%;
}
.block-editor-block-pattern-setup.view-mode-grid{
  padding-top:4px;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__toolbar{
  justify-content:center;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container{
  column-count:2;
  column-gap:24px;
  display:block;
  padding:0 32px;
  width:100%;
}
@media (min-width:1440px){
  .block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container{
    column-count:3;
  }
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-preview__container,.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container div[role=button]{
  cursor:pointer;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__item{
  scroll-margin:5px 0;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__item:hover .block-editor-block-preview__container{
  box-shadow:0 0 0 2px var(--wp-admin-theme-color);
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__item:focus .block-editor-block-preview__container{
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) #fff, 0 0 0 calc(var(--wp-admin-border-width-focus)*2) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:2px;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__item:focus .block-editor-block-pattern-setup-list__item-title,.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__item:hover .block-editor-block-pattern-setup-list__item-title{
  color:var(--wp-admin-theme-color);
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__list-item{
  break-inside:avoid-column;
  margin-bottom:24px;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__list-item .block-editor-block-pattern-setup-list__item-title{
  cursor:pointer;
  font-size:12px;
  padding-top:8px;
  text-align:center;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__list-item .block-editor-block-preview__container{
  border:1px solid #ddd;
  border-radius:4px;
  min-height:100px;
}
.block-editor-block-pattern-setup.view-mode-grid .block-editor-block-pattern-setup__container .block-editor-block-pattern-setup-list__list-item .block-editor-block-preview__content{
  width:100%;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__toolbar{
  align-items:center;
  align-self:stretch;
  background-color:#fff;
  border-top:1px solid #ddd;
  bottom:0;
  box-sizing:border-box;
  color:#1e1e1e;
  display:flex;
  flex-direction:row;
  height:60px;
  justify-content:space-between;
  margin:0;
  padding:16px;
  position:absolute;
  text-align:right;
  width:100%;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__toolbar .block-editor-block-pattern-setup__display-controls{
  display:flex;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__toolbar .block-editor-block-pattern-setup__actions,.block-editor-block-pattern-setup .block-editor-block-pattern-setup__toolbar .block-editor-block-pattern-setup__navigation{
  display:flex;
  width:calc(50% - 36px);
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__toolbar .block-editor-block-pattern-setup__actions{
  justify-content:flex-end;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container{
  box-sizing:border-box;
  display:flex;
  flex-direction:column;
  height:100%;
  width:100%;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container{
  height:100%;
  list-style:none;
  margin:0;
  overflow:hidden;
  padding:0;
  position:relative;
  transform-style:preserve-3d;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container *{
  box-sizing:border-box;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container .pattern-slide{
  background-color:#fff;
  height:100%;
  margin:auto;
  padding:0;
  position:absolute;
  top:0;
  transition:transform .5s,z-index .5s;
  width:100%;
  z-index:100;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container .pattern-slide.active-slide{
  opacity:1;
  position:relative;
  z-index:102;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container .pattern-slide.previous-slide{
  transform:translateX(100%);
  z-index:101;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .carousel-container .pattern-slide.next-slide{
  transform:translateX(-100%);
  z-index:101;
}
.block-editor-block-pattern-setup .block-editor-block-pattern-setup__container .block-list-appender{
  display:none;
}

.block-editor-block-pattern-setup__carousel,.block-editor-block-pattern-setup__grid{
  width:100%;
}

.block-editor-block-variation-transforms{
  padding:0 52px 16px 16px;
  width:100%;
}
.block-editor-block-variation-transforms .components-dropdown-menu__toggle{
  border:1px solid #757575;
  border-radius:2px;
  justify-content:right;
  min-height:30px;
  padding:6px 12px;
  position:relative;
  text-align:right;
  width:100%;
}
.block-editor-block-variation-transforms .components-dropdown-menu__toggle.components-dropdown-menu__toggle{
  padding-left:24px;
}
.block-editor-block-variation-transforms .components-dropdown-menu__toggle:focus:not(:disabled){
  border-color:var(--wp-admin-theme-color);
  box-shadow:0 0 0 calc(var(--wp-admin-border-width-focus) - 1px) var(--wp-admin-theme-color);
}
.block-editor-block-variation-transforms .components-dropdown-menu__toggle svg{
  height:100%;
  left:0;
  padding:0;
  position:absolute;
  top:0;
}

.block-editor-block-variation-transforms__popover .components-popover__content{
  min-width:230px;
}

.components-border-radius-control{
  margin-bottom:12px;
}
.components-border-radius-control legend{
  margin-bottom:8px;
}
.components-border-radius-control .components-border-radius-control__wrapper{
  align-items:flex-start;
  display:flex;
  justify-content:space-between;
}
.components-border-radius-control .components-border-radius-control__wrapper .components-border-radius-control__unit-control{
  flex-shrink:0;
  margin-bottom:0;
  margin-left:16px;
  width:calc(50% - 8px);
}
.components-border-radius-control .components-border-radius-control__wrapper .components-border-radius-control__range-control{
  flex:1;
  margin-left:12px;
}
.components-border-radius-control .components-border-radius-control__input-controls-wrapper{
  display:grid;
  gap:16px;
  grid-template-columns:repeat(2, minmax(0, 1fr));
  margin-left:12px;
}
.components-border-radius-control .component-border-radius-control__linked-button{
  display:flex;
  justify-content:center;
  margin-top:8px;
}
.components-border-radius-control .component-border-radius-control__linked-button svg{
  margin-left:0;
}

.block-editor-color-gradient-control .block-editor-color-gradient-control__color-indicator{
  margin-bottom:12px;
}

.block-editor-color-gradient-control__fieldset{
  min-width:0;
}

.block-editor-panel-color-gradient-settings.block-editor-panel-color-gradient-settings,.block-editor-panel-color-gradient-settings.block-editor-panel-color-gradient-settings>div:not(:first-of-type){
  display:block;
}

@media screen and (min-width:782px){
  .block-editor-panel-color-gradient-settings .components-circular-option-picker__swatches{
    display:grid;
    grid-template-columns:repeat(6, 28px);
  }
}
.block-editor-block-inspector .block-editor-panel-color-gradient-settings .components-base-control{
  margin-bottom:inherit;
}

.block-editor-panel-color-gradient-settings__dropdown-content .block-editor-color-gradient-control__panel{
  padding:16px;
  width:260px;
}

.block-editor-panel-color-gradient-settings__color-indicator{
  background:linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
}
.block-editor-tools-panel-color-gradient-settings__item{
  border-bottom:1px solid #ddd;
  border-left:1px solid #ddd;
  border-right:1px solid #ddd;
  max-width:100%;
  padding:0;
}
.block-editor-tools-panel-color-gradient-settings__item:nth-child(1 of .block-editor-tools-panel-color-gradient-settings__item){
  border-top:1px solid #ddd;
  border-top-left-radius:2px;
  border-top-right-radius:2px;
  margin-top:24px;
}
.block-editor-tools-panel-color-gradient-settings__item:nth-last-child(1 of .block-editor-tools-panel-color-gradient-settings__item){
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
}
.block-editor-tools-panel-color-gradient-settings__item>div,.block-editor-tools-panel-color-gradient-settings__item>div>button{
  border-radius:inherit;
}

.block-editor-tools-panel-color-gradient-settings__dropdown{
  display:block;
  padding:0;
}
.block-editor-tools-panel-color-gradient-settings__dropdown>button{
  height:auto;
  padding-bottom:10px;
  padding-top:10px;
  text-align:right;
}
.block-editor-tools-panel-color-gradient-settings__dropdown>button.is-open{
  background:#f0f0f0;
  color:var(--wp-admin-theme-color);
}
.block-editor-tools-panel-color-gradient-settings__dropdown .block-editor-panel-color-gradient-settings__color-name{
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.block-editor-panel-color-gradient-settings__dropdown{
  width:100%;
}
.block-editor-panel-color-gradient-settings__dropdown .component-color-indicator{
  flex-shrink:0;
}

.block-editor-date-format-picker{
  margin-bottom:16px;
}

.block-editor-date-format-picker__custom-format-select-control__custom-option{
  border-top:1px solid #ddd;
}

.block-editor-duotone-control__popover>.components-popover__content{
  padding:16px;
  width:260px;
}
.block-editor-duotone-control__popover .components-menu-group__label{
  padding:0;
}
.block-editor-duotone-control__popover .components-circular-option-picker__swatches{
  display:grid;
  gap:12px;
  grid-template-columns:repeat(6, 28px);
  justify-content:space-between;
}

.block-editor-duotone-control__unset-indicator{
  background:linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
}

.components-font-appearance-control [role=option]{
  color:#1e1e1e;
  text-transform:capitalize;
}

.block-editor-global-styles__toggle-icon{
  fill:currentColor;
}

.block-editor-global-styles__shadow-popover-container{
  width:230px;
}

.block-editor-global-styles__shadow__list{
  display:flex;
  flex-wrap:wrap;
  gap:12px;
  padding-bottom:8px;
}

.block-editor-global-styles__clear-shadow{
  text-align:left;
}

.block-editor-global-styles-filters-panel__dropdown,.block-editor-global-styles__shadow-dropdown{
  display:block;
  padding:0;
}
.block-editor-global-styles-filters-panel__dropdown button,.block-editor-global-styles__shadow-dropdown button{
  padding:8px;
  width:100%;
}
.block-editor-global-styles-filters-panel__dropdown button.is-open,.block-editor-global-styles__shadow-dropdown button.is-open{
  background-color:#f0f0f0;
}

.block-editor-global-styles__shadow-indicator{
  align-items:center;
  appearance:none;
  background:none;
  border:1px solid #e0e0e0;
  border-radius:2px;
  box-sizing:border-box;
  color:#2f2f2f;
  cursor:pointer;
  display:inline-flex;
  height:26px;
  padding:0;
  transform:scale(1);
  transition:transform .1s ease;
  width:26px;
  will-change:transform;
}
.block-editor-global-styles__shadow-indicator:focus{
  border:2px solid #757575;
}
.block-editor-global-styles__shadow-indicator:hover{
  transform:scale(1.2);
}
.block-editor-global-styles__shadow-indicator.unset{
  background:linear-gradient(45deg, #0000 48%, #ddd 0, #ddd 52%, #0000 0);
}

.block-editor-global-styles-advanced-panel__custom-css-input textarea{
  direction:ltr;
  font-family:Menlo,Consolas,monaco,monospace;
}

.block-editor-grid-visualizer.block-editor-grid-visualizer.block-editor-grid-visualizer{
  z-index:30;
}
.block-editor-grid-visualizer.block-editor-grid-visualizer.block-editor-grid-visualizer .components-popover__content *{
  pointer-events:none;
}
.block-editor-grid-visualizer.block-editor-grid-visualizer.block-editor-grid-visualizer.is-dropping-allowed .block-editor-grid-visualizer__drop-zone{
  pointer-events:all;
}
.block-editor-grid-visualizer.block-editor-grid-visualizer.block-editor-grid-visualizer .block-editor-inserter *{
  pointer-events:auto;
}

.block-editor-grid-visualizer__grid{
  display:grid;
}

.block-editor-grid-visualizer__cell{
  display:grid;
  position:relative;
}
.block-editor-grid-visualizer__cell .block-editor-inserter{
  bottom:0;
  color:inherit;
  left:0;
  overflow:hidden;
  position:absolute;
  right:0;
  top:0;
  z-index:32;
}
.block-editor-grid-visualizer__cell .block-editor-inserter .block-editor-grid-visualizer__appender{
  box-shadow:inset 0 0 0 1px color-mix(in srgb, currentColor 20%, #0000);
  color:inherit;
  height:100%;
  opacity:0;
  overflow:hidden;
  padding:0 !important;
  width:100%;
}
.block-editor-grid-visualizer__cell.is-highlighted .block-editor-grid-visualizer__drop-zone,.block-editor-grid-visualizer__cell.is-highlighted .block-editor-inserter{
  background:var(--wp-admin-theme-color);
}
.block-editor-grid-visualizer__cell .block-editor-grid-visualizer__appender:focus,.block-editor-grid-visualizer__cell:hover .block-editor-grid-visualizer__appender{
  background-color:color-mix(in srgb, currentColor 20%, #0000);
  opacity:1;
}

.block-editor-grid-visualizer__drop-zone{
  background:#cccccc1a;
  grid-column:1;
  grid-row:1;
  height:100%;
  min-height:8px;
  min-width:8px;
  width:100%;
}

.block-editor-grid-item-resizer.block-editor-grid-item-resizer.block-editor-grid-item-resizer{
  z-index:30;
}
.block-editor-grid-item-resizer.block-editor-grid-item-resizer.block-editor-grid-item-resizer .components-popover__content *{
  pointer-events:none;
}

.block-editor-grid-item-resizer__box{
  border:1px solid var(--wp-admin-theme-color);
}
.block-editor-grid-item-resizer__box .components-resizable-box__handle.components-resizable-box__handle.components-resizable-box__handle{
  pointer-events:all;
}

.block-editor-grid-item-mover__move-button-container{
  border:none;
  display:flex;
  justify-content:center;
  padding:0;
}
.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button{
  min-width:0 !important;
  padding-left:0;
  padding-right:0;
  width:24px;
}
.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button svg{
  min-width:24px;
}
.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:before{
  animation:components-button__appear-animation .1s ease;
  animation-fill-mode:forwards;
  border-radius:2px;
  content:"";
  display:block;
  height:32px;
  left:8px;
  position:absolute;
  right:8px;
  z-index:-1;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:before{
    animation-delay:0s;
    animation-duration:1ms;
  }
}
.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:focus,.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:focus:before,.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:focus:enabled{
  box-shadow:none;
  outline:none;
}
.block-editor-grid-item-mover__move-button-container .block-editor-grid-item-mover-button:focus-visible:before{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}

.block-editor-grid-item-mover__move-vertical-button-container{
  display:flex;
  position:relative;
}
@media (min-width:600px){
  .block-editor-grid-item-mover__move-vertical-button-container{
    flex-direction:column;
    justify-content:space-around;
  }
  .block-editor-grid-item-mover__move-vertical-button-container>.block-editor-grid-item-mover-button.block-editor-grid-item-mover-button{
    height:20px !important;
    min-width:0 !important;
    width:100%;
  }
  .block-editor-grid-item-mover__move-vertical-button-container>.block-editor-grid-item-mover-button.block-editor-grid-item-mover-button:before{
    height:calc(100% - 4px);
  }
  .block-editor-grid-item-mover__move-vertical-button-container .block-editor-grid-item-mover-button.is-down-button svg,.block-editor-grid-item-mover__move-vertical-button-container .block-editor-grid-item-mover-button.is-up-button svg{
    flex-shrink:0;
    height:20px;
  }
  .editor-collapsible-block-toolbar .block-editor-grid-item-mover__move-vertical-button-container{
    height:40px;
    position:relative;
    top:-5px;
  }
}

.show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container{
  position:relative;
}
@media (min-width:600px){
  .show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container:before{
    background:#e0e0e0;
    content:"";
    height:100%;
    position:absolute;
    top:0;
    width:1px;
  }
}
@media (min-width:782px){
  .show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container:before{
    background:#1e1e1e;
  }
}
.show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container.is-left{
  padding-left:6px;
}
.show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container.is-left:before{
  left:0;
}
.show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container.is-right{
  padding-right:6px;
}
.show-icon-labels .block-editor-grid-item-mover__move-horizontal-button-container.is-right:before{
  right:0;
}
@media (min-width:600px){
  .show-icon-labels .block-editor-grid-item-mover__move-vertical-button-container:before{
    background:#e0e0e0;
    content:"";
    height:1px;
    margin-top:-.5px;
    position:absolute;
    right:50%;
    top:50%;
    transform:translate(50%);
    width:100%;
  }
}
@media (min-width:782px){
  .show-icon-labels .block-editor-grid-item-mover__move-vertical-button-container:before{
    background:#1e1e1e;
  }
}
.show-icon-labels .block-editor-grid-item-mover-button{
  white-space:nowrap;
}
.show-icon-labels .editor-collapsible-block-toolbar .block-editor-grid-item-mover__move-horizontal-button-container:before{
  background:#ddd;
  height:24px;
  top:4px;
}
.show-icon-labels .editor-collapsible-block-toolbar .block-editor-grid-item-mover__move-vertical-button-container:before{
  background:#ddd;
  width:calc(100% - 24px);
}

.block-editor-height-control{
  border:0;
  margin:0;
  padding:0;
}

.block-editor-iframe__container{
  height:100%;
  width:100%;
}

.block-editor-iframe__scale-container{
  height:100%;
}

.block-editor-iframe__scale-container.is-zoomed-out{
  left:0;
  position:absolute;
  width:var(--wp-block-editor-iframe-zoom-out-scale-container-width, 100vw);
}

.block-editor-image-size-control{
  margin-bottom:1em;
}
.block-editor-image-size-control .block-editor-image-size-control__height,.block-editor-image-size-control .block-editor-image-size-control__width{
  margin-bottom:1.115em;
}

.block-editor-block-types-list__list-item{
  display:block;
  margin:0;
  padding:0;
  width:33.33%;
}
.block-editor-block-types-list__list-item.is-synced .components-button.block-editor-block-types-list__item:not(:disabled) .block-editor-block-icon.has-colors{
  color:var(--wp-block-synced-color);
}
.block-editor-block-types-list__list-item.is-synced .components-button.block-editor-block-types-list__item:not(:disabled):hover .block-editor-block-types-list__item-title{
  color:var(--wp-block-synced-color) !important;
  filter:brightness(.95);
}
.block-editor-block-types-list__list-item.is-synced .components-button.block-editor-block-types-list__item:not(:disabled):hover svg{
  color:var(--wp-block-synced-color) !important;
}
.block-editor-block-types-list__list-item.is-synced .components-button.block-editor-block-types-list__item:not(:disabled):after{
  background:var(--wp-block-synced-color);
}

.components-button.block-editor-block-types-list__item{
  align-items:stretch;
  background:#0000;
  color:#1e1e1e;
  cursor:pointer;
  display:flex;
  flex-direction:column;
  font-size:13px;
  height:auto;
  justify-content:center;
  padding:8px;
  position:relative;
  transition:all .05s ease-in-out;
  width:100%;
  word-break:break-word;
}
@media (prefers-reduced-motion:reduce){
  .components-button.block-editor-block-types-list__item{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.components-button.block-editor-block-types-list__item:disabled{
  cursor:default;
  opacity:.6;
}
.components-button.block-editor-block-types-list__item:not(:disabled):hover .block-editor-block-types-list__item-title{
  color:var(--wp-admin-theme-color) !important;
  filter:brightness(.95);
}
.components-button.block-editor-block-types-list__item:not(:disabled):hover svg{
  color:var(--wp-admin-theme-color) !important;
}
.components-button.block-editor-block-types-list__item:not(:disabled):hover:after{
  background:var(--wp-admin-theme-color);
  border-radius:2px;
  bottom:0;
  content:"";
  left:0;
  opacity:.04;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}
.components-button.block-editor-block-types-list__item:not(:disabled):focus{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.components-button.block-editor-block-types-list__item:not(:disabled).is-active{
  background:#1e1e1e;
  color:#fff;
  outline:2px solid #0000;
  outline-offset:-2px;
}

.block-editor-block-types-list__item-icon{
  color:#1e1e1e;
  padding:12px 20px;
  transition:all .05s ease-in-out;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-types-list__item-icon{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-block-types-list__item-icon .block-editor-block-icon{
  margin-left:auto;
  margin-right:auto;
}
.block-editor-block-types-list__item-icon svg{
  transition:all .15s ease-out;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-types-list__item-icon svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-block-types-list__list-item[draggable=true] .block-editor-block-types-list__item-icon{
  cursor:grab;
}

.block-editor-block-types-list__item-title{
  font-size:12px;
  hyphens:auto;
  padding:4px 2px 8px;
}

.show-icon-labels .block-editor-block-inspector__tabs [role=tablist] .components-button{
  justify-content:center;
}

.block-editor-inspector-popover-header{
  margin-bottom:16px;
}

.items-justified-left{
  justify-content:flex-start;
}

.items-justified-center{
  justify-content:center;
}

.items-justified-right{
  justify-content:flex-end;
}

.items-justified-space-between{
  justify-content:space-between;
}

@keyframes loadingpulse{
  0%{
    opacity:1;
  }
  50%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
.block-editor-link-control{
  min-width:350px;
  position:relative;
}
.components-popover__content .block-editor-link-control{
  max-width:350px;
  min-width:auto;
  width:90vw;
}
.show-icon-labels .block-editor-link-control .components-button.has-icon svg{
  display:none;
}
.show-icon-labels .block-editor-link-control .components-button.has-icon:before{
  content:attr(aria-label);
}
.show-icon-labels .block-editor-link-control .block-editor-link-control__search-item-top{
  flex-wrap:wrap;
  gap:4px;
}
.show-icon-labels .block-editor-link-control .block-editor-link-control__search-item-top .components-button.has-icon{
  padding:4px;
  width:auto;
}
.show-icon-labels .block-editor-link-control .is-preview .block-editor-link-control__search-item-header{
  margin-left:0;
  min-width:100%;
}

.block-editor-link-control__search-input-wrapper{
  margin-bottom:8px;
  position:relative;
}

.block-editor-link-control__search-input-container,.block-editor-link-control__search-input-wrapper{
  position:relative;
}

.block-editor-link-control__field{
  margin:16px;
}
.block-editor-link-control__field .components-base-control__label{
  color:#1e1e1e;
}

.block-editor-link-control__search-error{
  margin:-8px 16px 16px;
}

.block-editor-link-control__search-actions{
  padding:8px 16px 16px;
}

.block-editor-link-control__search-results-wrapper{
  position:relative;
}
.block-editor-link-control__search-results-wrapper:after,.block-editor-link-control__search-results-wrapper:before{
  content:"";
  display:block;
  left:16px;
  pointer-events:none;
  position:absolute;
  right:-1px;
  z-index:100;
}
.block-editor-link-control__search-results-wrapper:before{
  bottom:auto;
  height:8px;
  top:0;
}
.block-editor-link-control__search-results-wrapper:after{
  bottom:0;
  height:16px;
  top:auto;
}

.block-editor-link-control__search-results{
  margin-top:-16px;
  max-height:200px;
  overflow-y:auto;
  padding:8px;
}
.block-editor-link-control__search-results.is-loading{
  opacity:.2;
}

.block-editor-link-control__search-item.components-button.components-menu-item__button{
  height:auto;
  text-align:right;
}
.block-editor-link-control__search-item .components-menu-item__item{
  display:inline-block;
  overflow:hidden;
  text-overflow:ellipsis;
  width:100%;
}
.block-editor-link-control__search-item .components-menu-item__item mark{
  background-color:initial;
  color:inherit;
  font-weight:600;
}
.block-editor-link-control__search-item .components-menu-item__shortcut{
  color:#757575;
  text-transform:capitalize;
  white-space:nowrap;
}
.block-editor-link-control__search-item[aria-selected]{
  background:#f0f0f0;
}
.block-editor-link-control__search-item.is-current{
  background:#0000;
  border:0;
  cursor:default;
  flex-direction:column;
  padding:16px;
  width:100%;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-header{
  align-items:center;
  display:block;
  flex-direction:row;
  gap:8px;
  margin-left:8px;
  overflow-wrap:break-word;
  white-space:pre-wrap;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-header .block-editor-link-control__search-item-info{
  color:#757575;
  font-size:12px;
  line-height:1.1;
  word-break:break-all;
}
.block-editor-link-control__search-item.is-preview .block-editor-link-control__search-item-header{
  display:flex;
  flex:1;
}
.block-editor-link-control__search-item.is-error .block-editor-link-control__search-item-header{
  align-items:center;
}
.block-editor-link-control__search-item.is-url-title .block-editor-link-control__search-item-title{
  word-break:break-all;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-details{
  display:flex;
  flex-direction:column;
  gap:4px;
  justify-content:space-between;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-header .block-editor-link-control__search-item-icon{
  background-color:#f0f0f0;
  border-radius:2px;
  height:32px;
  width:32px;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-icon{
  align-items:center;
  display:flex;
  flex-shrink:0;
  justify-content:center;
  position:relative;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-icon img{
  width:16px;
}
.block-editor-link-control__search-item.is-error .block-editor-link-control__search-item-icon{
  max-height:32px;
  top:0;
  width:32px;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title{
  line-height:1.1;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title:focus{
  box-shadow:none;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title:focus-visible{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  text-decoration:none;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title mark{
  background-color:initial;
  color:inherit;
  font-weight:600;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title span{
  font-weight:400;
}
.block-editor-link-control__search-item .block-editor-link-control__search-item-title .components-external-link__icon{
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
  clip:rect(0, 0, 0, 0);
  border:0;
}

.block-editor-link-control__search-item-top{
  align-items:center;
  display:flex;
  flex-direction:row;
  width:100%;
}

.block-editor-link-control__search-item.is-fetching .block-editor-link-control__search-item-icon img,.block-editor-link-control__search-item.is-fetching .block-editor-link-control__search-item-icon svg{
  opacity:0;
}
.block-editor-link-control__search-item.is-fetching .block-editor-link-control__search-item-icon:before{
  animation:loadingpulse 1s linear infinite;
  animation-delay:.5s;
  background-color:#f0f0f0;
  border-radius:100%;
  bottom:0;
  content:"";
  display:block;
  left:0;
  position:absolute;
  right:0;
  top:0;
}

.block-editor-link-control__loading{
  align-items:center;
  display:flex;
  margin:16px;
}
.block-editor-link-control__loading .components-spinner{
  margin-top:0;
}

.components-button+.block-editor-link-control__search-create{
  overflow:visible;
  padding:12px 16px;
}
.components-button+.block-editor-link-control__search-create:before{
  content:"";
  display:block;
  position:absolute;
  right:0;
  top:-10px;
  width:100%;
}

.block-editor-link-control__search-create{
  align-items:center;
}
.block-editor-link-control__search-create .block-editor-link-control__search-item-title{
  margin-bottom:0;
}
.block-editor-link-control__search-create .block-editor-link-control__search-item-icon{
  top:0;
}

.block-editor-link-control__drawer-inner{
  display:flex;
  flex-basis:100%;
  flex-direction:column;
  position:relative;
}

.block-editor-link-control__setting{
  flex:1;
  margin-bottom:0;
  padding:8px 24px 8px 0;
}
.block-editor-link-control__setting .components-base-control__field{
  display:flex;
}
.block-editor-link-control__setting .components-base-control__field .components-checkbox-control__label{
  color:#1e1e1e;
}
.block-editor-link-control__setting input{
  margin-right:0;
}
.is-preview .block-editor-link-control__setting{
  padding:20px 0 8px 8px;
}

.block-editor-link-control__tools{
  margin-top:-16px;
  padding:8px 8px 0;
}
.block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle{
  gap:0;
  padding-right:0;
}
.block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle[aria-expanded=true]{
  color:#1e1e1e;
}
.block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle[aria-expanded=true] svg{
  transform:rotate(-90deg);
  transition:transform .1s ease;
  visibility:visible;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle[aria-expanded=true] svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle[aria-expanded=false] svg{
  transform:rotate(0deg);
  transition:transform .1s ease;
  visibility:visible;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-link-control__tools .components-button.block-editor-link-control__drawer-toggle[aria-expanded=false] svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-link-control .block-editor-link-control__search-input .components-spinner{
  display:block;
}
.block-editor-link-control .block-editor-link-control__search-input .components-spinner.components-spinner{
  bottom:auto;
  left:40px;
  position:absolute;
  right:auto;
  top:calc(50% - 8px);
}

.block-editor-link-control .block-editor-link-control__search-input-wrapper.has-actions .components-spinner{
  left:12px;
  top:calc(50% + 4px);
}

.block-editor-list-view-tree{
  border-collapse:collapse;
  margin:0;
  padding:0;
  width:100%;
}
.components-modal__content .block-editor-list-view-tree{
  margin:-12px -6px 0;
  width:calc(100% + 12px);
}
.block-editor-list-view-tree.is-dragging tbody{
  pointer-events:none;
}

.block-editor-list-view-leaf{
  position:relative;
  transform:translateY(0);
}
.block-editor-list-view-leaf.is-draggable,.block-editor-list-view-leaf.is-draggable .block-editor-list-view-block-contents{
  cursor:grab;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button[aria-expanded=true]{
  color:inherit;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button:hover{
  color:var(--wp-admin-theme-color);
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button svg{
  fill:currentColor;
}
@media (forced-colors:active){
  .block-editor-list-view-leaf .block-editor-list-view-block-select-button svg{
    fill:CanvasText;
  }
}
.is-dragging-components-draggable .block-editor-list-view-leaf:not(.is-selected) .block-editor-list-view-block-select-button:hover{
  color:inherit;
}
.block-editor-list-view-leaf.is-selected td{
  background:var(--wp-admin-theme-color);
}
.block-editor-list-view-leaf.is-selected.is-synced td{
  background:var(--wp-block-synced-color);
}
.block-editor-list-view-leaf.is-synced:not(.is-selected) .block-editor-list-view-block-contents .block-editor-block-icon,.block-editor-list-view-leaf.is-synced:not(.is-selected) .block-editor-list-view-block-contents:focus,.block-editor-list-view-leaf.is-synced:not(.is-selected) .block-editor-list-view-block-contents:hover{
  color:var(--wp-block-synced-color);
}
.block-editor-list-view-leaf.is-synced:not(.is-selected) .block-editor-list-view-block-contents:focus:after{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-block-synced-color);
}
.block-editor-list-view-leaf.is-selected .block-editor-list-view-block-contents,.block-editor-list-view-leaf.is-selected .components-button.has-icon{
  color:#fff;
}
.block-editor-list-view-leaf.is-selected .block-editor-list-view-block-contents:focus:after{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}
.block-editor-list-view-leaf.is-selected.is-synced .block-editor-list-view-block-contents:focus:after{
  box-shadow:inset 0 0 0 1px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-block-synced-color);
}
.block-editor-list-view-leaf.is-selected .block-editor-list-view-block__menu:focus{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) #fff;
}
.block-editor-list-view-leaf.is-first-selected td:first-child{
  border-top-right-radius:2px;
}
.block-editor-list-view-leaf.is-first-selected td:last-child{
  border-top-left-radius:2px;
}
.block-editor-list-view-leaf.is-last-selected td:first-child{
  border-bottom-right-radius:2px;
}
.block-editor-list-view-leaf.is-last-selected td:last-child{
  border-bottom-left-radius:2px;
}
.block-editor-list-view-leaf.is-branch-selected:not(.is-selected):not(.is-synced-branch){
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
}
.block-editor-list-view-leaf.is-synced-branch.is-branch-selected{
  background:rgba(var(--wp-block-synced-color--rgb), .04);
}
.block-editor-list-view-leaf.is-branch-selected.is-first-selected td:first-child{
  border-top-right-radius:2px;
}
.block-editor-list-view-leaf.is-branch-selected.is-first-selected td:last-child{
  border-top-left-radius:2px;
}
.block-editor-list-view-leaf[data-expanded=false].is-branch-selected.is-first-selected td:first-child{
  border-top-right-radius:2px;
}
.block-editor-list-view-leaf[data-expanded=false].is-branch-selected.is-first-selected td:last-child{
  border-top-left-radius:2px;
}
.block-editor-list-view-leaf[data-expanded=false].is-branch-selected.is-last-selected td:first-child{
  border-bottom-right-radius:2px;
}
.block-editor-list-view-leaf[data-expanded=false].is-branch-selected.is-last-selected td:last-child{
  border-bottom-left-radius:2px;
}
.block-editor-list-view-leaf.is-branch-selected:not(.is-selected) td{
  border-radius:0;
}
.block-editor-list-view-leaf.is-displacement-normal{
  transform:translateY(0);
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-displacement-normal{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-displacement-up{
  transform:translateY(-32px);
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-displacement-up{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-displacement-down{
  transform:translateY(32px);
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-displacement-down{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-after-dragged-blocks{
  transform:translateY(calc(var(--wp-admin--list-view-dragged-items-height, 32px)*-1));
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-after-dragged-blocks{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-after-dragged-blocks.is-displacement-up{
  transform:translateY(calc(-32px + var(--wp-admin--list-view-dragged-items-height, 32px)*-1));
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-after-dragged-blocks.is-displacement-up{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-after-dragged-blocks.is-displacement-down{
  transform:translateY(calc(32px + var(--wp-admin--list-view-dragged-items-height, 32px)*-1));
  transition:transform .2s;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-after-dragged-blocks.is-displacement-down{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.block-editor-list-view-leaf.is-dragging{
  opacity:0;
  pointer-events:none;
  right:0;
  z-index:-9999;
}
.block-editor-list-view-leaf .block-editor-list-view-block-contents{
  align-items:center;
  border-radius:2px;
  box-sizing:border-box;
  color:inherit;
  display:flex;
  font-family:inherit;
  font-size:13px;
  font-weight:400;
  height:32px;
  margin:0;
  padding:6px 0 6px 4px;
  position:relative;
  text-align:right;
  text-decoration:none;
  transition:box-shadow .1s linear;
  white-space:nowrap;
  width:100%;
}
.block-editor-list-view-leaf .block-editor-list-view-block-contents.is-dropping-before:before{
  border-top:4px solid var(--wp-admin-theme-color);
  content:"";
  left:0;
  pointer-events:none;
  position:absolute;
  right:0;
  top:-2px;
  transition:border-color .1s linear,border-style .1s linear,box-shadow .1s linear;
}
.components-modal__content .block-editor-list-view-leaf .block-editor-list-view-block-contents{
  padding-left:0;
  padding-right:0;
}
.block-editor-list-view-leaf .block-editor-list-view-block-contents:focus,.block-editor-list-view-leaf.is-nesting .block-editor-list-view-block-contents{
  box-shadow:none;
}
.block-editor-list-view-leaf .block-editor-list-view-block-contents:focus:after,.block-editor-list-view-leaf.is-nesting .block-editor-list-view-block-contents:after{
  border-radius:inherit;
  bottom:0;
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  content:"";
  left:-29px;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
  z-index:2;
}
.block-editor-list-view-leaf.has-single-cell .block-editor-list-view-block-contents:focus:after{
  left:0;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu:focus,.block-editor-list-view-leaf.is-nesting .block-editor-list-view__menu{
  box-shadow:inset 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  z-index:1;
}
.block-editor-list-view-leaf.is-visible .block-editor-list-view-block-contents{
  animation:__wp-base-styles-fade-in .08s linear 0s;
  animation-fill-mode:forwards;
  opacity:1;
}
@keyframes __wp-base-styles-fade-in{
  0%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf.is-visible .block-editor-list-view-block-contents{
    animation-delay:0s;
    animation-duration:1ms;
  }
}
.block-editor-list-view-leaf .block-editor-block-icon{
  flex:0 0 24px;
  margin-left:4px;
}
.block-editor-list-view-leaf .block-editor-list-view-block__contents-cell,.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell{
  padding:0;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell{
  line-height:0;
  vertical-align:middle;
  width:36px;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell>*,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell>*{
  opacity:0;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell.is-visible>*,.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell:focus-within>*,.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell:hover>*,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell.is-visible>*,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell:focus-within>*,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell:hover>*{
  opacity:1;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell,.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell .components-button.has-icon,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell,.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell .components-button.has-icon{
  min-width:24px;
  padding:0;
  width:24px;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell{
  padding-left:4px;
}
.block-editor-list-view-leaf .block-editor-list-view-block__menu-cell .components-button.has-icon{
  height:24px;
}
.block-editor-list-view-leaf .block-editor-list-view-block__mover-cell-alignment-wrapper{
  align-items:center;
  display:flex;
  flex-direction:column;
  height:100%;
}
.block-editor-list-view-leaf .block-editor-block-mover-button{
  height:24px;
  position:relative;
  width:36px;
}
.block-editor-list-view-leaf .block-editor-block-mover-button svg{
  height:24px;
  position:relative;
}
.block-editor-list-view-leaf .block-editor-block-mover-button.is-up-button{
  align-items:flex-end;
  margin-top:-6px;
}
.block-editor-list-view-leaf .block-editor-block-mover-button.is-up-button svg{
  bottom:-4px;
}
.block-editor-list-view-leaf .block-editor-block-mover-button.is-down-button{
  align-items:flex-start;
  margin-bottom:-6px;
}
.block-editor-list-view-leaf .block-editor-block-mover-button.is-down-button svg{
  top:-4px;
}
.block-editor-list-view-leaf .block-editor-block-mover-button:before{
  height:16px;
  left:0;
  min-width:100%;
  right:0;
}
.block-editor-list-view-leaf .block-editor-inserter__toggle{
  background:#1e1e1e;
  color:#fff;
  height:24px;
  margin:6px 1px 6px 6px;
  min-width:24px;
}
.block-editor-list-view-leaf .block-editor-inserter__toggle:active{
  color:#fff;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__label-wrapper svg{
  position:relative;
  right:2px;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__title{
  flex:1;
  position:relative;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__title .components-truncate{
  position:absolute;
  transform:translateY(-50%);
  width:100%;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__anchor-wrapper{
  max-width:min(110px, 40%);
  position:relative;
  width:100%;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__anchor{
  background:#0000001a;
  border-radius:1px;
  box-sizing:border-box;
  left:0;
  max-width:100%;
  padding:2px 6px;
  position:absolute;
  transform:translateY(-50%);
}
.block-editor-list-view-leaf.is-selected .block-editor-list-view-block-select-button__anchor{
  background:#0000004d;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__lock,.block-editor-list-view-leaf .block-editor-list-view-block-select-button__sticky{
  line-height:0;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__images{
  display:flex;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__image{
  background-size:cover;
  border-radius:1px;
  height:18px;
  width:18px;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__image:not(:only-child){
  box-shadow:0 0 0 2px #fff;
}
.block-editor-list-view-leaf .block-editor-list-view-block-select-button__image:not(:first-child){
  margin-right:-6px;
}
.block-editor-list-view-leaf.is-selected .block-editor-list-view-block-select-button__image:not(:only-child){
  box-shadow:0 0 0 2px var(--wp-admin-theme-color);
}

.block-editor-list-view-draggable-chip{
  opacity:.8;
}

.block-editor-list-view-appender__cell .block-editor-list-view-appender__container,.block-editor-list-view-appender__cell .block-editor-list-view-block__contents-container,.block-editor-list-view-block__contents-cell .block-editor-list-view-appender__container,.block-editor-list-view-block__contents-cell .block-editor-list-view-block__contents-container{
  display:flex;
}

.block-editor-list-view__expander{
  cursor:pointer;
  height:24px;
  width:24px;
}

.block-editor-list-view-leaf[aria-level] .block-editor-list-view__expander{
  margin-right:192px;
}

.block-editor-list-view-leaf[aria-level="1"] .block-editor-list-view__expander{
  margin-right:0;
}

.block-editor-list-view-leaf[aria-level="2"] .block-editor-list-view__expander{
  margin-right:24px;
}

.block-editor-list-view-leaf[aria-level="3"] .block-editor-list-view__expander{
  margin-right:48px;
}

.block-editor-list-view-leaf[aria-level="4"] .block-editor-list-view__expander{
  margin-right:72px;
}

.block-editor-list-view-leaf[aria-level="5"] .block-editor-list-view__expander{
  margin-right:96px;
}

.block-editor-list-view-leaf[aria-level="6"] .block-editor-list-view__expander{
  margin-right:120px;
}

.block-editor-list-view-leaf[aria-level="7"] .block-editor-list-view__expander{
  margin-right:144px;
}

.block-editor-list-view-leaf[aria-level="8"] .block-editor-list-view__expander{
  margin-right:168px;
}

.block-editor-list-view-leaf .block-editor-list-view__expander{
  visibility:hidden;
}

.block-editor-list-view-leaf[data-expanded=true] .block-editor-list-view__expander svg{
  transform:rotate(-90deg);
  transition:transform .2s ease;
  visibility:visible;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf[data-expanded=true] .block-editor-list-view__expander svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-list-view-leaf[data-expanded=false] .block-editor-list-view__expander svg{
  transform:rotate(0deg);
  transition:transform .2s ease;
  visibility:visible;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-list-view-leaf[data-expanded=false] .block-editor-list-view__expander svg{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-list-view-drop-indicator{
  pointer-events:none;
}
.block-editor-list-view-drop-indicator .block-editor-list-view-drop-indicator__line{
  background:var(--wp-admin-theme-color);
  border-radius:4px;
  height:4px;
}

.block-editor-list-view-drop-indicator--preview{
  pointer-events:none;
}
.block-editor-list-view-drop-indicator--preview .components-popover__content{
  overflow:hidden !important;
}
.block-editor-list-view-drop-indicator--preview .block-editor-list-view-drop-indicator__line{
  background:rgba(var(--wp-admin-theme-color--rgb), .04);
  border-radius:4px;
  height:32px;
  overflow:hidden;
}
.block-editor-list-view-drop-indicator--preview .block-editor-list-view-drop-indicator__line--darker{
  background:rgba(var(--wp-admin-theme-color--rgb), .09);
}

.block-editor-list-view-placeholder{
  height:32px;
  margin:0;
  padding:0;
}

.list-view-appender .block-editor-inserter__toggle{
  background-color:#1e1e1e;
  color:#fff;
  height:24px;
  margin:8px 24px 0 0;
  min-width:24px;
  padding:0;
}
.list-view-appender .block-editor-inserter__toggle:focus,.list-view-appender .block-editor-inserter__toggle:hover{
  background:var(--wp-admin-theme-color);
  color:#fff;
}

.list-view-appender__description,.modal-open .block-editor-media-replace-flow__options{
  display:none;
}

.block-editor-media-replace-flow__indicator{
  margin-right:4px;
}

.block-editor-media-flow__url-input{
  margin-left:-8px;
  margin-right:-8px;
  padding:16px;
}
.block-editor-media-flow__url-input.has-siblings{
  border-top:1px solid #1e1e1e;
  margin-top:8px;
  padding-bottom:8px;
}
.block-editor-media-flow__url-input .block-editor-media-replace-flow__image-url-label{
  display:block;
  margin-bottom:8px;
  top:16px;
}
.block-editor-media-flow__url-input .block-editor-link-control{
  width:300px;
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-url-input{
  margin:0;
  padding:0;
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__search-item-info,.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__search-item-title{
  max-width:200px;
  white-space:nowrap;
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__tools{
  justify-content:flex-end;
  padding:16px var(--wp-admin-border-width-focus) var(--wp-admin-border-width-focus);
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__search-item.is-current{
  padding:0;
  width:auto;
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__search-input.block-editor-link-control__search-input input[type=text]{
  margin:0;
  width:100%;
}
.block-editor-media-flow__url-input .block-editor-link-control .block-editor-link-control__search-actions{
  padding:8px 0 0;
}

.block-editor-media-flow__error{
  max-width:255px;
  padding:0 20px 20px;
}
.block-editor-media-flow__error .components-with-notices-ui{
  max-width:255px;
}
.block-editor-media-flow__error .components-with-notices-ui .components-notice__content{
  overflow:hidden;
  word-wrap:break-word;
}
.block-editor-media-flow__error .components-with-notices-ui .components-notice__dismiss{
  left:10px;
  position:absolute;
}

.block-editor-multi-selection-inspector__card{
  align-items:flex-start;
  display:flex;
  padding:16px;
}

.block-editor-multi-selection-inspector__card-content{
  flex-grow:1;
}

.block-editor-multi-selection-inspector__card-title{
  font-weight:500;
  margin-bottom:5px;
}

.block-editor-multi-selection-inspector__card-description{
  font-size:13px;
}

.block-editor-multi-selection-inspector__card .block-editor-block-icon{
  height:24px;
  margin-left:10px;
  margin-right:-2px;
  padding:0 3px;
  width:36px;
}

.block-editor-responsive-block-control{
  border-bottom:1px solid #ccc;
  margin-bottom:28px;
  padding-bottom:14px;
}
.block-editor-responsive-block-control:last-child{
  border-bottom:0;
  padding-bottom:0;
}

.block-editor-responsive-block-control__title{
  margin:0 -3px .6em 0;
}

.block-editor-responsive-block-control__label{
  font-weight:600;
  margin-bottom:.6em;
  margin-right:-3px;
}

.block-editor-responsive-block-control__inner{
  margin-right:-1px;
}

.block-editor-responsive-block-control__toggle{
  margin-right:1px;
}

.block-editor-responsive-block-control .components-base-control__help{
  border:0;
  clip:rect(1px, 1px, 1px, 1px);
  clip-path:inset(50%);
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
  word-wrap:normal !important;
}

.components-popover.block-editor-rich-text__inline-format-toolbar{
  z-index:99998;
}
.components-popover.block-editor-rich-text__inline-format-toolbar .components-popover__content{
  border-radius:2px;
  box-shadow:none;
  margin-bottom:8px;
  min-width:auto;
  outline:none;
  width:auto;
}
.components-popover.block-editor-rich-text__inline-format-toolbar .components-toolbar{
  border-radius:2px;
}
.components-popover.block-editor-rich-text__inline-format-toolbar .components-toolbar-group{
  background:none;
}
.components-popover.block-editor-rich-text__inline-format-toolbar .components-dropdown-menu__toggle,.components-popover.block-editor-rich-text__inline-format-toolbar .components-toolbar__control{
  min-height:48px;
  min-width:48px;
  padding-left:12px;
  padding-right:12px;
}

.block-editor-rich-text__inline-format-toolbar-group .components-dropdown-menu__toggle{
  justify-content:center;
}

.show-icon-labels .block-editor-rich-text__inline-format-toolbar-group .components-button.has-icon{
  width:auto;
}
.show-icon-labels .block-editor-rich-text__inline-format-toolbar-group .components-button.has-icon svg{
  display:none;
}
.show-icon-labels .block-editor-rich-text__inline-format-toolbar-group .components-button.has-icon:after{
  content:attr(aria-label);
}

.block-editor-skip-to-selected-block{
  position:absolute;
  top:-9999em;
}
.block-editor-skip-to-selected-block:focus{
  background:#f1f1f1;
  font-size:14px;
  font-weight:600;
  z-index:100000;
}

.block-editor-tabbed-sidebar{
  background-color:#fff;
  display:flex;
  flex-direction:column;
  flex-grow:1;
  height:100%;
  overflow:hidden;
}

.block-editor-tabbed-sidebar__tablist-and-close-button{
  border-bottom:1px solid #ddd;
  display:flex;
  justify-content:space-between;
  padding-left:12px;
}

.block-editor-tabbed-sidebar__close-button{
  align-self:center;
  background:#fff;
  order:1;
}

.block-editor-tabbed-sidebar__tablist{
  margin-bottom:-1px;
}

.block-editor-tabbed-sidebar__tabpanel{
  display:flex;
  flex-direction:column;
  flex-grow:1;
  overflow-y:auto;
  scrollbar-gutter:auto;
}

.block-editor-tool-selector__help{
  border-top:1px solid #ddd;
  color:#757575;
  margin:8px -8px -8px;
  min-width:280px;
  padding:16px;
}

.block-editor-block-list__block .block-editor-url-input,.block-editor-url-input,.components-popover .block-editor-url-input{
  flex-grow:1;
  padding:1px;
  position:relative;
}
@media (min-width:600px){
  .block-editor-block-list__block .block-editor-url-input,.block-editor-url-input,.components-popover .block-editor-url-input{
    min-width:300px;
    width:auto;
  }
}
.block-editor-block-list__block .block-editor-url-input.is-full-width,.block-editor-block-list__block .block-editor-url-input.is-full-width__suggestions,.block-editor-url-input.is-full-width,.block-editor-url-input.is-full-width__suggestions,.components-popover .block-editor-url-input.is-full-width,.components-popover .block-editor-url-input.is-full-width__suggestions{
  width:100%;
}
.block-editor-block-list__block .block-editor-url-input .components-spinner,.block-editor-url-input .components-spinner,.components-popover .block-editor-url-input .components-spinner{
  left:8px;
  margin:0;
  position:absolute;
  top:calc(50% - 8px);
}

.block-editor-url-input__suggestions{
  max-height:200px;
  overflow-y:auto;
  padding:4px 0;
  transition:all .15s ease-in-out;
  width:302px;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-url-input__suggestions{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-url-input .components-spinner,.block-editor-url-input__suggestions{
  display:none;
}
@media (min-width:600px){
  .block-editor-url-input .components-spinner,.block-editor-url-input__suggestions{
    display:grid;
  }
}

.block-editor-url-input__suggestion{
  background:#fff;
  border:none;
  box-shadow:none;
  color:#757575;
  cursor:pointer;
  display:block;
  font-size:13px;
  height:auto;
  min-height:36px;
  text-align:right;
  width:100%;
}
.block-editor-url-input__suggestion:hover{
  background:#ddd;
}
.block-editor-url-input__suggestion.is-selected,.block-editor-url-input__suggestion:focus{
  background:var(--wp-admin-theme-color-darker-20);
  color:#fff;
  outline:none;
}

.components-toolbar-group>.block-editor-url-input__button,.components-toolbar>.block-editor-url-input__button{
  position:inherit;
}

.block-editor-url-input__button .block-editor-url-input__back{
  margin-left:4px;
  overflow:visible;
}
.block-editor-url-input__button .block-editor-url-input__back:after{
  background:#ddd;
  content:"";
  display:block;
  height:24px;
  left:-1px;
  position:absolute;
  width:1px;
}

.block-editor-url-input__button-modal{
  background:#fff;
  border:1px solid #ddd;
  box-shadow:0 1px 1px #00000008,0 1px 2px #00000005,0 3px 3px #00000005,0 4px 4px #00000003;
}

.block-editor-url-input__button-modal-line{
  align-items:flex-start;
  display:flex;
  flex-direction:row;
  flex-grow:1;
  flex-shrink:1;
  min-width:0;
}

.block-editor-url-popover__additional-controls{
  border-top:1px solid #1e1e1e;
  padding:8px;
}

.block-editor-url-popover__input-container{
  padding:8px;
}

.block-editor-url-popover__row{
  align-items:center;
  display:flex;
  gap:4px;
}

.block-editor-url-popover__row>:not(.block-editor-url-popover__settings-toggle){
  flex-grow:1;
  gap:8px;
}

.block-editor-url-popover__additional-controls .components-button.has-icon{
  height:auto;
  padding-left:8px;
  padding-right:8px;
  text-align:right;
}
.block-editor-url-popover__additional-controls .components-button.has-icon>svg{
  margin-left:8px;
}

.block-editor-url-popover__settings-toggle{
  flex-shrink:0;
}
.block-editor-url-popover__settings-toggle[aria-expanded=true] .dashicon{
  transform:rotate(-180deg);
}

.block-editor-url-popover__settings{
  border-top:1px solid #1e1e1e;
  display:block;
  padding:16px;
}

.block-editor-url-popover__link-editor,.block-editor-url-popover__link-viewer{
  display:flex;
}

.block-editor-url-popover__link-viewer-url{
  align-items:center;
  display:flex;
  flex-grow:1;
  flex-shrink:1;
  margin-left:8px;
  max-width:350px;
  min-width:150px;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}
.block-editor-url-popover__link-viewer-url.has-invalid-link{
  color:#cc1818;
}

.block-editor-url-popover__expand-on-click{
  align-items:center;
  display:flex;
  min-width:350px;
  white-space:nowrap;
}
.block-editor-url-popover__expand-on-click .text{
  flex-grow:1;
}
.block-editor-url-popover__expand-on-click .text p{
  line-height:16px;
  margin:0;
}
.block-editor-url-popover__expand-on-click .text p.description{
  color:#757575;
  font-size:12px;
}
.block-editor-hooks__block-hooks .components-toggle-control .components-h-stack .components-h-stack{
  flex-direction:row;
}
.block-editor-hooks__block-hooks .block-editor-hooks__block-hooks-helptext{
  color:#757575;
  font-size:12px;
  margin-bottom:16px;
}

div.block-editor-bindings__panel{
  grid-template-columns:repeat(auto-fit, minmax(100%, 1fr));
}
div.block-editor-bindings__panel button:hover .block-editor-bindings__item span{
  color:inherit;
}

.border-block-support-panel .single-column{
  grid-column:span 1;
}
.color-block-support-panel .block-editor-contrast-checker{
  grid-column:span 2;
  margin-top:16px;
  order:9999;
}
.color-block-support-panel .block-editor-contrast-checker .components-notice__content{
  margin-left:0;
}
.color-block-support-panel.color-block-support-panel .color-block-support-panel__inner-wrapper{
  row-gap:0;
}
.color-block-support-panel .block-editor-tools-panel-color-gradient-settings__item.first{
  margin-top:0;
}

.dimensions-block-support-panel .single-column{
  grid-column:span 1;
}

.block-editor-hooks__layout-constrained .components-base-control{
  margin-bottom:0;
}

.block-editor-hooks__layout-constrained-helptext{
  color:#757575;
  font-size:12px;
  margin-bottom:0;
}

.block-editor-hooks__flex-layout-justification-controls,.block-editor-hooks__flex-layout-orientation-controls{
  margin-bottom:12px;
}
.block-editor-hooks__flex-layout-justification-controls legend,.block-editor-hooks__flex-layout-orientation-controls legend{
  margin-bottom:8px;
}

.block-editor__spacing-visualizer{
  border-color:var(--wp-admin-theme-color);
  border-style:solid;
  bottom:0;
  box-sizing:border-box;
  left:0;
  opacity:.5;
  pointer-events:none;
  position:absolute;
  right:0;
  top:0;
}

.typography-block-support-panel .single-column{
  grid-column:span 1;
}
.block-editor-block-toolbar{
  display:flex;
  flex-grow:1;
  overflow-x:auto;
  overflow-y:hidden;
  position:relative;
  transition:border-color .1s linear,box-shadow .1s linear;
  width:100%;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-block-toolbar{
    transition-delay:0s;
    transition-duration:0s;
  }
}
@media (min-width:600px){
  .block-editor-block-toolbar{
    overflow:inherit;
  }
}
.block-editor-block-toolbar .components-toolbar,.block-editor-block-toolbar .components-toolbar-group{
  background:none;
  border:0;
  border-left:1px solid #ddd;
  margin-bottom:-1px;
  margin-top:-1px;
}
.block-editor-block-toolbar.is-connected .block-editor-block-switcher .components-button:before{
  background:color-mix(in srgb, var(--wp-block-synced-color) 10%, #0000);
  border-radius:2px;
}
.block-editor-block-toolbar.is-connected .block-editor-block-switcher .components-button .block-editor-block-icon,.block-editor-block-toolbar.is-connected .components-toolbar-button.block-editor-block-switcher__no-switcher-icon:disabled .block-editor-block-icon.has-colors,.block-editor-block-toolbar.is-synced .block-editor-block-switcher .components-button .block-editor-block-icon,.block-editor-block-toolbar.is-synced .components-toolbar-button.block-editor-block-switcher__no-switcher-icon:disabled .block-editor-block-icon.has-colors{
  color:var(--wp-block-synced-color);
}
.block-editor-block-toolbar:has(>:last-child:empty)>:nth-last-child(2),.block-editor-block-toolbar:has(>:last-child:empty)>:nth-last-child(2) .components-toolbar,.block-editor-block-toolbar:has(>:last-child:empty)>:nth-last-child(2) .components-toolbar-group,.block-editor-block-toolbar>:last-child,.block-editor-block-toolbar>:last-child .components-toolbar,.block-editor-block-toolbar>:last-child .components-toolbar-group{
  border-left:none;
}
.block-editor-block-toolbar .components-toolbar-group:empty{
  display:none;
}

.block-editor-block-contextual-toolbar{
  background-color:#fff;
  display:block;
  flex-shrink:3;
  position:sticky;
  top:0;
  width:100%;
  z-index:31;
}
.block-editor-block-contextual-toolbar.components-accessible-toolbar{
  border:none;
  border-radius:0;
}
.block-editor-block-contextual-toolbar.is-unstyled{
  box-shadow:0 1px 0 0 rgba(0,0,0,.133);
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar{
  overflow:auto;
  overflow-y:hidden;
  scrollbar-color:#e0e0e0 #0000;
  scrollbar-gutter:stable both-edges;
  scrollbar-gutter:auto;
  scrollbar-width:thin;
  will-change:transform;
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar::-webkit-scrollbar{
  height:12px;
  width:12px;
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar::-webkit-scrollbar-track{
  background-color:initial;
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar::-webkit-scrollbar-thumb{
  background-clip:padding-box;
  background-color:#e0e0e0;
  border:3px solid #0000;
  border-radius:8px;
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar:focus-within::-webkit-scrollbar-thumb,.block-editor-block-contextual-toolbar .block-editor-block-toolbar:focus::-webkit-scrollbar-thumb,.block-editor-block-contextual-toolbar .block-editor-block-toolbar:hover::-webkit-scrollbar-thumb{
  background-color:#949494;
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar:focus,.block-editor-block-contextual-toolbar .block-editor-block-toolbar:focus-within,.block-editor-block-contextual-toolbar .block-editor-block-toolbar:hover{
  scrollbar-color:#949494 #0000;
}
@media (hover:none){
  .block-editor-block-contextual-toolbar .block-editor-block-toolbar{
    scrollbar-color:#949494 #0000;
  }
}
.block-editor-block-contextual-toolbar .block-editor-block-toolbar>:last-child .components-toolbar-group:after,.block-editor-block-contextual-toolbar .block-editor-block-toolbar>:last-child .components-toolbar:after,.block-editor-block-contextual-toolbar .block-editor-block-toolbar>:last-child:after{
  display:none;
}
.block-editor-block-contextual-toolbar>.block-editor-block-toolbar{
  flex-grow:0;
  width:auto;
}
.block-editor-block-contextual-toolbar .block-editor-block-parent-selector{
  margin-bottom:-1px;
  margin-top:-1px;
  position:relative;
}
.block-editor-block-contextual-toolbar .block-editor-block-parent-selector:after{
  align-items:center;
  background-color:#1e1e1e;
  border-radius:100%;
  content:"";
  display:inline-flex;
  height:2px;
  left:0;
  position:absolute;
  top:15px;
  width:2px;
}

.block-editor-block-toolbar__block-controls .block-editor-block-switcher .components-dropdown-menu__toggle .block-editor-block-icon,.block-editor-block-toolbar__block-controls .block-editor-block-switcher__no-switcher-icon .block-editor-block-icon{
  margin:0 !important;
  width:24px !important;
}
.block-editor-block-toolbar__block-controls .components-toolbar-group{
  padding:0;
}

.block-editor-block-toolbar .components-toolbar,.block-editor-block-toolbar .components-toolbar-group,.block-editor-rich-text__inline-format-toolbar-group .components-toolbar,.block-editor-rich-text__inline-format-toolbar-group .components-toolbar-group{
  display:flex;
  flex-wrap:nowrap;
}

.block-editor-block-toolbar__slot{
  display:inline-flex;
}

.show-icon-labels .block-editor-block-toolbar .components-button.has-icon{
  width:auto;
}
.show-icon-labels .block-editor-block-toolbar .components-button.has-icon svg{
  display:none;
}
.show-icon-labels .block-editor-block-toolbar .components-button.has-icon:after{
  content:attr(aria-label);
  font-size:12px;
}
.show-icon-labels .components-accessible-toolbar .components-toolbar-group>div:first-child:last-child>.components-button.has-icon{
  padding-left:6px;
  padding-right:6px;
}
.show-icon-labels .block-editor-block-switcher .components-dropdown-menu__toggle .block-editor-block-icon,.show-icon-labels .block-editor-block-switcher__no-switcher-icon .block-editor-block-icon{
  height:0 !important;
  min-width:0 !important;
  width:0 !important;
}
.show-icon-labels .block-editor-block-parent-selector .block-editor-block-parent-selector__button{
  border-bottom-left-radius:0;
  border-top-left-radius:0;
  padding-left:12px;
  padding-right:12px;
  text-wrap:nowrap;
}
.show-icon-labels .block-editor-block-parent-selector .block-editor-block-parent-selector__button .block-editor-block-icon{
  width:0;
}
.show-icon-labels .block-editor-block-mover .block-editor-block-mover__move-button-container{
  position:relative;
  width:auto;
}
@media (min-width:600px){
  .show-icon-labels .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container:before{
    background:#e0e0e0;
    content:"";
    height:1px;
    margin-top:-.5px;
    position:absolute;
    right:50%;
    top:50%;
    transform:translate(50%);
    width:100%;
  }
}
@media (min-width:782px){
  .show-icon-labels .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover__move-button-container:before{
    background:#1e1e1e;
  }
}
.show-icon-labels .block-editor-block-mover.is-horizontal .block-editor-block-mover-button,.show-icon-labels .block-editor-block-mover.is-horizontal .block-editor-block-mover__move-button-container{
  padding-left:6px;
  padding-right:6px;
}
.show-icon-labels .block-editor-block-mover:not(.is-horizontal) .block-editor-block-mover-button{
  padding-left:8px;
  padding-right:8px;
}
.show-icon-labels .block-editor-block-toolbar__block-controls .block-editor-block-mover{
  border-right:1px solid #ddd;
  margin-left:-6px;
  margin-right:6px;
  white-space:nowrap;
}
.show-icon-labels .block-editor-block-mover .block-editor-block-mover__drag-handle.has-icon{
  padding-left:12px;
  padding-right:12px;
}
.show-icon-labels .block-editor-block-contextual-toolbar .block-editor-block-mover.is-horizontal .block-editor-block-mover-button.block-editor-block-mover-button{
  width:auto;
}
.show-icon-labels .components-toolbar,.show-icon-labels .components-toolbar-group{
  flex-shrink:1;
}
.show-icon-labels .block-editor-rich-text__inline-format-toolbar-group .components-button+.components-button{
  margin-right:6px;
}

.block-editor-inserter{
  background:none;
  border:none;
  display:inline-block;
  font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
  font-size:13px;
  line-height:0;
  padding:0;
}
@media (min-width:782px){
  .block-editor-inserter{
    position:relative;
  }
}

.block-editor-inserter__main-area{
  gap:16px;
  height:100%;
  position:relative;
}
.block-editor-inserter__main-area.show-as-tabs{
  gap:0;
}
@media (min-width:782px){
  .block-editor-inserter__main-area .block-editor-tabbed-sidebar{
    width:350px;
  }
}

.block-editor-inserter__popover.is-quick .components-popover__content{
  border:none;
  box-shadow:0 1px 1px #00000008,0 1px 2px #00000005,0 3px 3px #00000005,0 4px 4px #00000003;
  outline:none;
}
.block-editor-inserter__popover.is-quick .components-popover__content .block-editor-inserter__quick-inserter>*{
  border-left:1px solid #ccc;
  border-right:1px solid #ccc;
}
.block-editor-inserter__popover.is-quick .components-popover__content .block-editor-inserter__quick-inserter>:first-child{
  border-radius:4px 4px 0 0;
  border-top:1px solid #ccc;
}
.block-editor-inserter__popover.is-quick .components-popover__content .block-editor-inserter__quick-inserter>:last-child{
  border-bottom:1px solid #ccc;
  border-radius:0 0 4px 4px;
}
.block-editor-inserter__popover.is-quick .components-popover__content .block-editor-inserter__quick-inserter>.components-button{
  border:1px solid #1e1e1e;
}

.block-editor-inserter__popover .block-editor-inserter__menu{
  margin:-12px;
}
.block-editor-inserter__popover .block-editor-inserter__menu .block-editor-inserter__tablist{
  top:60px;
}
.block-editor-inserter__popover .block-editor-inserter__menu .block-editor-inserter__main-area{
  height:auto;
  overflow:visible;
}
.block-editor-inserter__popover .block-editor-inserter__menu .block-editor-inserter__preview-container{
  display:none;
}

.block-editor-inserter__toggle.components-button{
  align-items:center;
  border:none;
  cursor:pointer;
  display:inline-flex;
  outline:none;
  padding:0;
  transition:color .2s ease;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-inserter__toggle.components-button{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-inserter__menu{
  height:100%;
  overflow:visible;
  position:relative;
}
@media (min-width:782px){
  .block-editor-inserter__menu.show-panel{
    width:630px;
  }
}

.block-editor-inserter__inline-elements{
  margin-top:-1px;
}

.block-editor-inserter__menu.is-bottom:after{
  border-bottom-color:#fff;
}

.components-popover.block-editor-inserter__popover{
  z-index:99999;
}

.block-editor-inserter__search{
  padding:16px 16px 0;
}

.block-editor-inserter__no-tab-container{
  flex-grow:1;
  overflow-y:auto;
  position:relative;
}

.block-editor-inserter__panel-header{
  align-items:center;
  display:inline-flex;
  padding:16px 16px 0;
  position:relative;
}

.block-editor-inserter__panel-content{
  padding:16px;
}

.block-editor-inserter__panel-title,.block-editor-inserter__panel-title button{
  color:#757575;
  font-size:11px;
  font-weight:500;
  margin:0 0 0 12px;
  text-transform:uppercase;
}

.block-editor-inserter__panel-dropdown select.components-select-control__input.components-select-control__input.components-select-control__input{
  height:36px;
  line-height:36px;
}

.block-editor-inserter__panel-dropdown select{
  border:none;
}

.block-editor-inserter__reusable-blocks-panel{
  position:relative;
  text-align:left;
}

.block-editor-inserter__no-results,.block-editor-inserter__patterns-loading{
  padding:32px;
  text-align:center;
}

.block-editor-inserter__no-results-icon{
  fill:#949494;
}

.block-editor-inserter__child-blocks{
  padding:0 16px;
}

.block-editor-inserter__parent-block-header{
  align-items:center;
  display:flex;
}
.block-editor-inserter__parent-block-header h2{
  font-size:13px;
}
.block-editor-inserter__parent-block-header .block-editor-block-icon{
  margin-left:8px;
}

.block-editor-inserter__preview-container__popover{
  top:16px !important;
}

.block-editor-inserter__preview-container{
  display:none;
  max-height:calc(100% - 32px);
  overflow-y:hidden;
  padding:16px;
  width:280px;
}
@media (min-width:782px){
  .block-editor-inserter__preview-container{
    display:block;
  }
}
.block-editor-inserter__preview-container .block-editor-inserter__media-list__list-item{
  height:100%;
}
.block-editor-inserter__preview-container .block-editor-block-card{
  padding-bottom:4px;
  padding-left:0;
  padding-right:0;
}

.block-editor-inserter__insertable-blocks-at-selection{
  border-bottom:1px solid #e0e0e0;
}

.block-editor-inserter__block-patterns-tabs-container,.block-editor-inserter__media-tabs-container{
  display:flex;
  flex-direction:column;
  height:100%;
  justify-content:space-between;
  padding:16px;
}

.block-editor-inserter__category-tablist{
  border:none;
  display:flex;
  flex-direction:column;
  margin-bottom:8px;
}
.block-editor-inserter__category-tablist div[role=listitem]:last-child{
  margin-top:auto;
}
.block-editor-inserter__category-tablist[aria-orientation=vertical]:after{
  content:none;
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab{
  display:block;
  font-weight:inherit;
  height:auto;
  padding:8px 12px 8px 4px;
  position:relative;
  text-align:right;
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab[aria-selected=true]{
  color:var(--wp-admin-theme-color);
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab[aria-selected=true] .components-flex-item{
  filter:brightness(.95);
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab[aria-selected=true] svg{
  fill:var(--wp-admin-theme-color);
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab[aria-selected=true]:after{
  background:var(--wp-admin-theme-color);
  border-radius:2px;
  bottom:0;
  content:"";
  display:block;
  height:100%;
  left:0;
  opacity:.04;
  outline:none;
  position:absolute;
  right:0;
  top:0;
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab:focus-visible,.block-editor-inserter__category-tablist .block-editor-inserter__category-tab:focus:not(:disabled){
  border-radius:2px;
  box-shadow:0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
  outline-offset:0;
}
.block-editor-inserter__category-tablist .block-editor-inserter__category-tab:after,.block-editor-inserter__category-tablist .block-editor-inserter__category-tab:before{
  display:none;
}

.block-editor-inserter__category-panel{
  display:flex;
  flex-direction:column;
  outline:1px solid #0000;
  padding:0 16px;
}
@media (min-width:782px){
  .block-editor-inserter__category-panel{
    background:#f0f0f0;
    border-right:1px solid #e0e0e0;
    border-top:1px solid #e0e0e0;
    height:calc(100% + 1px);
    padding:0;
    position:absolute;
    right:350px;
    top:-1px;
    width:280px;
  }
  .block-editor-inserter__category-panel .block-editor-block-patterns-list,.block-editor-inserter__category-panel .block-editor-inserter__media-list{
    padding:0 24px 16px;
  }
}

.block-editor-inserter__patterns-category-panel-header{
  padding:8px 0;
}
@media (min-width:782px){
  .block-editor-inserter__patterns-category-panel-header{
    padding:8px 24px;
  }
}

.block-editor-inserter__patterns-category-no-results{
  margin-top:24px;
}

.block-editor-block-patterns-list,.block-editor-inserter__media-list{
  flex-grow:1;
  height:100%;
  overflow-y:auto;
}

.block-editor-inserter__preview-content{
  align-items:center;
  background:#f0f0f0;
  display:grid;
  flex-grow:1;
}

.block-editor-inserter__preview-content-missing{
  align-items:center;
  background:#f0f0f0;
  border-radius:2px;
  color:#757575;
  display:flex;
  flex:1;
  justify-content:center;
  min-height:144px;
}

.block-editor-inserter__tips{
  border-top:1px solid #ddd;
  flex-shrink:0;
  padding:16px;
  position:relative;
}

.block-editor-inserter__quick-inserter{
  max-width:100%;
  width:100%;
}
@media (min-width:782px){
  .block-editor-inserter__quick-inserter{
    width:350px;
  }
}

.block-editor-inserter__quick-inserter-results .block-editor-inserter__panel-header{
  float:right;
  height:0;
  padding:0;
}

.block-editor-inserter__quick-inserter.has-expand .block-editor-inserter__panel-content,.block-editor-inserter__quick-inserter.has-search .block-editor-inserter__panel-content{
  padding:16px;
}

.block-editor-inserter__quick-inserter-patterns .block-editor-block-patterns-list{
  display:grid;
  grid-template-columns:1fr 1fr;
  grid-gap:8px;
}
.block-editor-inserter__quick-inserter-patterns .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  margin-bottom:0;
}
.block-editor-inserter__quick-inserter-patterns .block-editor-block-patterns-list .block-editor-inserter__media-list__list-item{
  min-height:100px;
}

.block-editor-inserter__quick-inserter-separator{
  border-top:1px solid #ddd;
}

.block-editor-inserter__popover.is-quick>.components-popover__content{
  padding:0;
}

.block-editor-inserter__quick-inserter-expand.components-button{
  background:#1e1e1e;
  border-radius:0;
  color:#fff;
  display:block;
  width:100%;
}
.block-editor-inserter__quick-inserter-expand.components-button:hover{
  color:#fff;
}
.block-editor-inserter__quick-inserter-expand.components-button:active{
  color:#ccc;
}
.block-editor-inserter__quick-inserter-expand.components-button.components-button:focus:not(:disabled){
  background:var(--wp-admin-theme-color);
  border-color:var(--wp-admin-theme-color);
  box-shadow:none;
}

.block-editor-block-patterns-explorer__sidebar{
  bottom:0;
  overflow-x:visible;
  overflow-y:scroll;
  padding:24px 32px 32px;
  position:absolute;
  right:0;
  top:72px;
  width:280px;
}
.block-editor-block-patterns-explorer__sidebar__categories-list__item{
  display:block;
  height:48px;
  text-align:right;
  width:100%;
}
.block-editor-block-patterns-explorer__search{
  margin-bottom:32px;
}
.block-editor-block-patterns-explorer__search-results-count{
  padding-bottom:32px;
}
.block-editor-block-patterns-explorer__list{
  margin-right:280px;
  padding:24px 0 32px;
}
.block-editor-block-patterns-explorer__list .block-editor-patterns__sync-status-filter .components-input-control__container{
  width:380px;
}
.block-editor-block-patterns-explorer .block-editor-block-patterns-list{
  display:grid;
  grid-gap:32px;
  grid-template-columns:repeat(1, 1fr);
  margin-bottom:16px;
}
@media (min-width:1080px){
  .block-editor-block-patterns-explorer .block-editor-block-patterns-list{
    grid-template-columns:repeat(2, 1fr);
  }
}
@media (min-width:1440px){
  .block-editor-block-patterns-explorer .block-editor-block-patterns-list{
    grid-template-columns:repeat(3, 1fr);
  }
}
.block-editor-block-patterns-explorer .block-editor-block-patterns-list .block-editor-block-patterns-list__list-item{
  min-height:240px;
}
.block-editor-block-patterns-explorer .block-editor-block-patterns-list .block-editor-inserter__media-list__list-item{
  height:inherit;
  max-height:800px;
  min-height:100px;
}

.components-heading.block-editor-inserter__patterns-category-panel-title{
  font-weight:500;
}

.block-editor-inserter__media-library-button.components-button,.block-editor-inserter__patterns-explore-button.components-button{
  justify-content:center;
  margin-top:16px;
  padding:16px;
  width:100%;
}

.block-editor-inserter__media-panel{
  display:flex;
  flex-direction:column;
  min-height:100%;
  padding:0 16px;
}
@media (min-width:782px){
  .block-editor-inserter__media-panel{
    padding:0;
  }
}
.block-editor-inserter__media-panel .block-editor-inserter__media-panel-spinner{
  align-items:center;
  display:flex;
  flex:1;
  height:100%;
  justify-content:center;
}
.block-editor-inserter__media-panel .block-editor-inserter__media-panel-search{
  margin-bottom:24px;
}
@media (min-width:782px){
  .block-editor-inserter__media-panel .block-editor-inserter__media-panel-search{
    margin-bottom:0;
    padding:16px 24px;
  }
  .block-editor-inserter__media-panel .block-editor-inserter__media-panel-search:not(:focus-within){
    --wp-components-color-background:#fff;
  }
}

.block-editor-inserter__media-list__list-item{
  cursor:pointer;
  margin-bottom:24px;
  position:relative;
}
.block-editor-inserter__media-list__list-item.is-placeholder{
  min-height:100px;
}
.block-editor-inserter__media-list__list-item[draggable=true] .block-editor-inserter__media-list__list-item{
  cursor:grab;
}
.block-editor-inserter__media-list__list-item.is-hovered .block-editor-inserter__media-list__item-preview>*{
  outline-color:#0000004d;
}
.block-editor-inserter__media-list__list-item.is-hovered .block-editor-inserter__media-list__item-preview-options>button{
  display:block;
}
.block-editor-inserter__media-list__list-item .block-editor-inserter__media-list__item-preview-options{
  left:8px;
  position:absolute;
  top:8px;
}
.block-editor-inserter__media-list__list-item .block-editor-inserter__media-list__item-preview-options>button{
  background:#fff;
  display:none;
}
.block-editor-inserter__media-list__list-item .block-editor-inserter__media-list__item-preview-options>button.is-opened,.block-editor-inserter__media-list__list-item .block-editor-inserter__media-list__item-preview-options>button:focus{
  display:block;
}
.block-editor-inserter__media-list__list-item .block-editor-inserter__media-list__item-preview-options>button:hover{
  box-shadow:inset 0 0 0 2px #fff, 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
  outline:2px solid #0000;
}

.block-editor-inserter__media-list__item{
  height:100%;
}
.block-editor-inserter__media-list__item .block-editor-inserter__media-list__item-preview{
  align-items:center;
  border-radius:2px;
  display:flex;
  overflow:hidden;
}
.block-editor-inserter__media-list__item .block-editor-inserter__media-list__item-preview>*{
  margin:0 auto;
  max-width:100%;
  outline:1px solid #0000001a;
  outline-offset:-1px;
}
.block-editor-inserter__media-list__item .block-editor-inserter__media-list__item-preview .block-editor-inserter__media-list__item-preview-spinner{
  align-items:center;
  background:#ffffffb3;
  display:flex;
  height:100%;
  justify-content:center;
  pointer-events:none;
  position:absolute;
  width:100%;
}
.block-editor-inserter__media-list__item:focus .block-editor-inserter__media-list__item-preview>*{
  outline-color:var(--wp-admin-theme-color);
  outline-offset:calc(-1*var(--wp-admin-border-width-focus));
  outline-width:var(--wp-admin-border-width-focus);
  transition:outline .1s linear;
}
@media (prefers-reduced-motion:reduce){
  .block-editor-inserter__media-list__item:focus .block-editor-inserter__media-list__item-preview>*{
    transition-delay:0s;
    transition-duration:0s;
  }
}

.block-editor-inserter__media-list__item-preview-options__popover .components-menu-item__button .components-menu-item__item{
  min-width:auto;
}

.block-editor-inserter__mobile-tab-navigation{
  height:100%;
  padding:16px;
}
.block-editor-inserter__mobile-tab-navigation>*{
  height:100%;
}

@media (min-width:600px){
  .block-editor-inserter-media-tab-media-preview-inserter-external-image-modal{
    max-width:480px;
  }
}
.block-editor-inserter-media-tab-media-preview-inserter-external-image-modal p{
  margin:0;
}

.block-editor-inserter__hint{
  margin:16px 16px 0;
}

.block-editor-patterns__sync-status-filter .components-input-control__container select.components-select-control__input{
  height:40px;
}

.block-editor-inserter__pattern-panel-placeholder{
  display:none;
}

.block-editor-inserter__menu.is-zoom-out{
  display:flex;
}
@media (min-width:782px){
  .block-editor-inserter__menu.is-zoom-out.show-panel:after{
    content:"";
    display:block;
    height:100%;
    width:300px;
  }
}

@media (max-width:959px){
  .show-icon-labels .block-editor-block-patterns-explorer .block-editor-patterns__grid-pagination,.show-icon-labels .block-editor-block-patterns-explorer .block-editor-patterns__grid-pagination .block-editor-patterns__grid-pagination-next,.show-icon-labels .block-editor-block-patterns-explorer .block-editor-patterns__grid-pagination .block-editor-patterns__grid-pagination-previous{
    flex-direction:column;
  }
}
.show-icon-labels .block-editor-inserter__category-panel .block-editor-patterns__grid-pagination,.show-icon-labels .block-editor-inserter__category-panel .block-editor-patterns__grid-pagination .block-editor-patterns__grid-pagination-next,.show-icon-labels .block-editor-inserter__category-panel .block-editor-patterns__grid-pagination .block-editor-patterns__grid-pagination-previous{
  flex-direction:column;
}

.block-editor-tabbed-sidebar__tabpanel .block-editor-inserter__help-text{
  padding:0 24px 16px;
}

.spacing-sizes-control .spacing-sizes-control__custom-value-input,.spacing-sizes-control .spacing-sizes-control__label{
  margin-bottom:0;
}
.spacing-sizes-control .is-marked .components-range-control__track{
  transition:width .1s ease;
}
@media (prefers-reduced-motion:reduce){
  .spacing-sizes-control .is-marked .components-range-control__track{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.spacing-sizes-control .is-marked .components-range-control__thumb-wrapper{
  transition:right .1s ease;
}
@media (prefers-reduced-motion:reduce){
  .spacing-sizes-control .is-marked .components-range-control__thumb-wrapper{
    transition-delay:0s;
    transition-duration:0s;
  }
}
.spacing-sizes-control .spacing-sizes-control__custom-value-range,.spacing-sizes-control .spacing-sizes-control__range-control{
  flex:1;
  margin-bottom:0;
}
.spacing-sizes-control .components-range-control__mark{
  background-color:#fff;
  height:4px;
  top:-4px;
  transform:translateX(50%);
  width:2px;
  z-index:1;
}
.spacing-sizes-control .components-range-control__marks{
  margin-top:17px;
}
.spacing-sizes-control .components-range-control__thumb-wrapper{
  z-index:3;
}

.spacing-sizes-control__header{
  height:16px;
  margin-bottom:12px;
}

.spacing-sizes-control__dropdown{
  height:24px;
}

.spacing-sizes-control__custom-select-control,.spacing-sizes-control__custom-value-input{
  flex:1;
}

.spacing-sizes-control__custom-toggle,.spacing-sizes-control__icon{
  flex:0 0 auto;
}

.spacing-sizes-control__icon{
  margin-right:-4px;
}

body.admin-color-light{
  --wp-admin-theme-color:#0085ba;
  --wp-admin-theme-color--rgb:0, 133, 186;
  --wp-admin-theme-color-darker-10:#0073a1;
  --wp-admin-theme-color-darker-10--rgb:0, 115, 161;
  --wp-admin-theme-color-darker-20:#006187;
  --wp-admin-theme-color-darker-20--rgb:0, 97, 135;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-light{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-modern{
  --wp-admin-theme-color:#3858e9;
  --wp-admin-theme-color--rgb:56, 88, 233;
  --wp-admin-theme-color-darker-10:#2145e6;
  --wp-admin-theme-color-darker-10--rgb:33, 69, 230;
  --wp-admin-theme-color-darker-20:#183ad6;
  --wp-admin-theme-color-darker-20--rgb:24, 58, 214;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-modern{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-blue{
  --wp-admin-theme-color:#096484;
  --wp-admin-theme-color--rgb:9, 100, 132;
  --wp-admin-theme-color-darker-10:#07526c;
  --wp-admin-theme-color-darker-10--rgb:7, 82, 108;
  --wp-admin-theme-color-darker-20:#064054;
  --wp-admin-theme-color-darker-20--rgb:6, 64, 84;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-blue{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-coffee{
  --wp-admin-theme-color:#46403c;
  --wp-admin-theme-color--rgb:70, 64, 60;
  --wp-admin-theme-color-darker-10:#383330;
  --wp-admin-theme-color-darker-10--rgb:56, 51, 48;
  --wp-admin-theme-color-darker-20:#2b2724;
  --wp-admin-theme-color-darker-20--rgb:43, 39, 36;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-coffee{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ectoplasm{
  --wp-admin-theme-color:#523f6d;
  --wp-admin-theme-color--rgb:82, 63, 109;
  --wp-admin-theme-color-darker-10:#46365d;
  --wp-admin-theme-color-darker-10--rgb:70, 54, 93;
  --wp-admin-theme-color-darker-20:#3a2c4d;
  --wp-admin-theme-color-darker-20--rgb:58, 44, 77;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ectoplasm{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-midnight{
  --wp-admin-theme-color:#e14d43;
  --wp-admin-theme-color--rgb:225, 77, 67;
  --wp-admin-theme-color-darker-10:#dd382d;
  --wp-admin-theme-color-darker-10--rgb:221, 56, 45;
  --wp-admin-theme-color-darker-20:#d02c21;
  --wp-admin-theme-color-darker-20--rgb:208, 44, 33;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-midnight{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-ocean{
  --wp-admin-theme-color:#627c83;
  --wp-admin-theme-color--rgb:98, 124, 131;
  --wp-admin-theme-color-darker-10:#576e74;
  --wp-admin-theme-color-darker-10--rgb:87, 110, 116;
  --wp-admin-theme-color-darker-20:#4c6066;
  --wp-admin-theme-color-darker-20--rgb:76, 96, 102;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-ocean{
    --wp-admin-border-width-focus:1.5px;
  }
}

body.admin-color-sunrise{
  --wp-admin-theme-color:#dd823b;
  --wp-admin-theme-color--rgb:221, 130, 59;
  --wp-admin-theme-color-darker-10:#d97426;
  --wp-admin-theme-color-darker-10--rgb:217, 116, 38;
  --wp-admin-theme-color-darker-20:#c36922;
  --wp-admin-theme-color-darker-20--rgb:195, 105, 34;
  --wp-admin-border-width-focus:2px;
}
@media (min-resolution:192dpi){
  body.admin-color-sunrise{
    --wp-admin-border-width-focus:1.5px;
  }
}