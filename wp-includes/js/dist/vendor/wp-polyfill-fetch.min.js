!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.WHATWGFetch={})}(this,(function(t){"use strict";var e,r,o="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||"undefined"!=typeof global&&global||{},n="URLSearchParams"in o,s="Symbol"in o&&"iterator"in Symbol,i="FileReader"in o&&"Blob"in o&&function(){try{return new Blob,!0}catch(t){return!1}}(),a="FormData"in o,h="ArrayBuffer"in o;function u(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function f(t){return"string"!=typeof t?String(t):t}function d(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return s&&(e[Symbol.iterator]=function(){return e}),e}function c(t){this.map={},t instanceof c?t.forEach((function(t,e){this.append(e,t)}),this):Array.isArray(t)?t.forEach((function(t){if(2!=t.length)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(e){this.append(e,t[e])}),this)}function y(t){if(!t._noBody)return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)}function l(t){return new Promise((function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}}))}function p(t){var e=new FileReader,r=l(e);return e.readAsArrayBuffer(t),r}function b(t){var e;return t.slice?t.slice(0):((e=new Uint8Array(t.byteLength)).set(new Uint8Array(t)),e.buffer)}function m(){return this.bodyUsed=!1,this._initBody=function(t){var e;this.bodyUsed=this.bodyUsed,(this._bodyInit=t)?"string"==typeof t?this._bodyText=t:i&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:a&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:n&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():h&&i&&(e=t)&&DataView.prototype.isPrototypeOf(e)?(this._bodyArrayBuffer=b(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):h&&(ArrayBuffer.prototype.isPrototypeOf(t)||r(t))?this._bodyArrayBuffer=b(t):this._bodyText=t=Object.prototype.toString.call(t):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):n&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i&&(this.blob=function(){var t=y(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer)return y(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer));if(i)return this.blob().then(p);throw new Error("could not read as ArrayBuffer")},this.text=function(){var t,e,r,o=y(this);if(o)return o;if(this._bodyBlob)return o=this._bodyBlob,e=l(t=new FileReader),r=(r=/charset=([A-Za-z0-9_-]+)/.exec(o.type))?r[1]:"utf-8",t.readAsText(o,r),e;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=new Array(e.length),o=0;o<e.length;o++)r[o]=String.fromCharCode(e[o]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},a&&(this.formData=function(){return this.text().then(A)}),this.json=function(){return this.text().then(JSON.parse)},this}h&&(e=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],r=ArrayBuffer.isView||function(t){return t&&-1<e.indexOf(Object.prototype.toString.call(t))}),c.prototype.append=function(t,e){t=u(t),e=f(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},c.prototype.delete=function(t){delete this.map[u(t)]},c.prototype.get=function(t){return t=u(t),this.has(t)?this.map[t]:null},c.prototype.has=function(t){return this.map.hasOwnProperty(u(t))},c.prototype.set=function(t,e){this.map[u(t)]=f(e)},c.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},c.prototype.keys=function(){var t=[];return this.forEach((function(e,r){t.push(r)})),d(t)},c.prototype.values=function(){var t=[];return this.forEach((function(e){t.push(e)})),d(t)},c.prototype.entries=function(){var t=[];return this.forEach((function(e,r){t.push([r,e])})),d(t)},s&&(c.prototype[Symbol.iterator]=c.prototype.entries);var w=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function E(t,e){if(!(this instanceof E))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var r,n=(e=e||{}).body;if(t instanceof E){if(t.bodyUsed)throw new TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new c(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,n||null==t._bodyInit||(n=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",!e.headers&&this.headers||(this.headers=new c(e.headers)),this.method=(r=(t=e.method||this.method||"GET").toUpperCase(),-1<w.indexOf(r)?r:t),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal||function(){if("AbortController"in o)return(new AbortController).signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&n)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(n),"GET"!==this.method&&"HEAD"!==this.method||"no-store"!==e.cache&&"no-cache"!==e.cache||((r=/([?&])_=[^&]*/).test(this.url)?this.url=this.url.replace(r,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime())}function A(t){var e=new FormData;return t.trim().split("&").forEach((function(t){var r;t&&(r=(t=t.split("=")).shift().replace(/\+/g," "),t=t.join("=").replace(/\+/g," "),e.append(decodeURIComponent(r),decodeURIComponent(t)))})),e}function g(t,e){if(!(this instanceof g))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e=e||{},this.type="default",this.status=void 0===e.status?200:e.status,this.status<200||599<this.status)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=200<=this.status&&this.status<300,this.statusText=void 0===e.statusText?"":""+e.statusText,this.headers=new c(e.headers),this.url=e.url||"",this._initBody(t)}E.prototype.clone=function(){return new E(this,{body:this._bodyInit})},m.call(E.prototype),m.call(g.prototype),g.prototype.clone=function(){return new g(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new c(this.headers),url:this.url})},g.error=function(){var t=new g(null,{status:200,statusText:""});return t.ok=!1,t.status=0,t.type="error",t};var T=[301,302,303,307,308];g.redirect=function(t,e){if(-1===T.indexOf(e))throw new RangeError("Invalid status code");return new g(null,{status:e,headers:{location:t}})},t.DOMException=o.DOMException;try{new t.DOMException}catch(d){t.DOMException=function(t,e){this.message=t,this.name=e,e=Error(t),this.stack=e.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}function _(e,r){return new Promise((function(n,s){var a=new E(e,r);if(a.signal&&a.signal.aborted)return s(new t.DOMException("Aborted","AbortError"));var d,y=new XMLHttpRequest;function l(){y.abort()}y.onload=function(){var t,e,r={statusText:y.statusText,headers:(t=y.getAllResponseHeaders()||"",e=new c,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var r=(t=t.split(":")).shift().trim();if(r){t=t.join(":").trim();try{e.append(r,t)}catch(t){console.warn("Response "+t.message)}}})),e)},o=(0===a.url.indexOf("file://")&&(y.status<200||599<y.status)?r.status=200:r.status=y.status,r.url="responseURL"in y?y.responseURL:r.headers.get("X-Request-URL"),"response"in y?y.response:y.responseText);setTimeout((function(){n(new g(o,r))}),0)},y.onerror=function(){setTimeout((function(){s(new TypeError("Network request failed"))}),0)},y.ontimeout=function(){setTimeout((function(){s(new TypeError("Network request timed out"))}),0)},y.onabort=function(){setTimeout((function(){s(new t.DOMException("Aborted","AbortError"))}),0)},y.open(a.method,function(t){try{return""===t&&o.location.href?o.location.href:t}catch(e){return t}}(a.url),!0),"include"===a.credentials?y.withCredentials=!0:"omit"===a.credentials&&(y.withCredentials=!1),"responseType"in y&&(i?y.responseType="blob":h&&(y.responseType="arraybuffer")),r&&"object"==typeof r.headers&&!(r.headers instanceof c||o.Headers&&r.headers instanceof o.Headers)?(d=[],Object.getOwnPropertyNames(r.headers).forEach((function(t){d.push(u(t)),y.setRequestHeader(t,f(r.headers[t]))})),a.headers.forEach((function(t,e){-1===d.indexOf(e)&&y.setRequestHeader(e,t)}))):a.headers.forEach((function(t,e){y.setRequestHeader(e,t)})),a.signal&&(a.signal.addEventListener("abort",l),y.onreadystatechange=function(){4===y.readyState&&a.signal.removeEventListener("abort",l)}),y.send(void 0===a._bodyInit?null:a._bodyInit)}))}_.polyfill=!0,o.fetch||(o.fetch=_,o.Headers=c,o.Request=E,o.Response=g),t.Headers=c,t.Request=E,t.Response=g,t.fetch=_,Object.defineProperty(t,"__esModule",{value:!0})}));