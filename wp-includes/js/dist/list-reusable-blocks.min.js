/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t);const n=window.wp.element,o=window.wp.i18n;var r=function(){return r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function s(e){return e.toLowerCase()}var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],i=/[^A-Z0-9]+/gi;function l(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function c(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,o=void 0===n?a:n,r=t.stripRegexp,c=void 0===r?i:r,p=t.transform,d=void 0===p?s:p,u=t.delimiter,w=void 0===u?" ":u,f=l(l(e,o,"$1\0$2"),c,"\0"),m=0,b=f.length;"\0"===f.charAt(m);)m++;for(;"\0"===f.charAt(b-1);)b--;return f.slice(m,b).split("\0").map(d).join(w)}(e,r({delimiter:"."},t))}const p=window.wp.apiFetch;var d=e.n(p);const u=window.wp.blob;const w=async function(e){const t=await d()({path:"/wp/v2/types/wp_block"}),n=await d()({path:`/wp/v2/${t.rest_base}/${e}?context=edit`}),o=n.title.raw,s=n.content.raw,a=n.wp_pattern_sync_status,i=JSON.stringify({__file:"wp_block",title:o,content:s,syncStatus:a},null,2),l=(void 0===p&&(p={}),c(o,r({delimiter:"-"},p))+".json");var p;(0,u.downloadBlob)(l,i,"application/json")},f=window.wp.compose,m=window.wp.components;const b=async function(e){const t=await function(e){const t=new window.FileReader;return new Promise((n=>{t.onload=()=>{n(t.result)},t.readAsText(e)}))}(e);let n;try{n=JSON.parse(t)}catch(e){throw new Error("Invalid JSON file")}if("wp_block"!==n.__file||!n.title||!n.content||"string"!=typeof n.title||"string"!=typeof n.content||n.syncStatus&&"string"!=typeof n.syncStatus)throw new Error("Invalid pattern JSON file");const o=await d()({path:"/wp/v2/types/wp_block"});return await d()({path:`/wp/v2/${o.rest_base}`,data:{title:n.title,content:n.content,status:"publish",meta:"unsynced"===n.syncStatus?{wp_pattern_sync_status:n.syncStatus}:void 0},method:"POST"})},_=window.ReactJSXRuntime;const v=(0,f.withInstanceId)((function({instanceId:e,onUpload:t}){const r="list-reusable-blocks-import-form-"+e,s=(0,n.useRef)(),[a,i]=(0,n.useState)(!1),[l,c]=(0,n.useState)(null),[p,d]=(0,n.useState)(null);return(0,_.jsxs)("form",{className:"list-reusable-blocks-import-form",onSubmit:e=>{e.preventDefault(),p&&(i({isLoading:!0}),b(p).then((e=>{s&&(i(!1),t(e))})).catch((e=>{if(!s)return;let t;switch(e.message){case"Invalid JSON file":t=(0,o.__)("Invalid JSON file");break;case"Invalid pattern JSON file":t=(0,o.__)("Invalid pattern JSON file");break;default:t=(0,o.__)("Unknown error")}i(!1),c(t)})))},ref:s,children:[l&&(0,_.jsx)(m.Notice,{status:"error",onRemove:()=>{c(null)},children:l}),(0,_.jsx)("label",{htmlFor:r,className:"list-reusable-blocks-import-form__label",children:(0,o.__)("File")}),(0,_.jsx)("input",{id:r,type:"file",onChange:e=>{d(e.target.files[0]),c(null)}}),(0,_.jsx)(m.Button,{__next40pxDefaultSize:!0,type:"submit",isBusy:a,accessibleWhenDisabled:!0,disabled:!p||a,variant:"secondary",className:"list-reusable-blocks-import-form__button",children:(0,o._x)("Import","button label")})]})}));const y=function({onUpload:e}){return(0,_.jsx)(m.Dropdown,{popoverProps:{placement:"bottom-start"},contentClassName:"list-reusable-blocks-import-dropdown__content",renderToggle:({isOpen:e,onToggle:t})=>(0,_.jsx)(m.Button,{size:"compact",className:"list-reusable-blocks-import-dropdown__button","aria-expanded":e,onClick:t,variant:"primary",children:(0,o.__)("Import from JSON")}),renderContent:({onClose:t})=>(0,_.jsx)(v,{onUpload:(0,f.pipe)(t,e)})})};document.body.addEventListener("click",(e=>{e.target.classList.contains("wp-list-reusable-blocks__export")&&(e.preventDefault(),w(e.target.dataset.id))})),document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelector(".page-title-action");if(!e)return;const t=document.createElement("div");t.className="list-reusable-blocks__container",e.parentNode.insertBefore(t,e),(0,n.createRoot)(t).render((0,_.jsx)(n.StrictMode,{children:(0,_.jsx)(y,{onUpload:()=>{const e=document.createElement("div");e.className="notice notice-success is-dismissible",e.innerHTML=`<p>${(0,o.__)("Pattern imported successfully!")}</p>`;const t=document.querySelector(".wp-header-end");t&&t.parentNode.insertBefore(e,t)}})}))})),(window.wp=window.wp||{}).listReusableBlocks=t})();