/*! This file is auto-generated */
(()=>{"use strict";var t={r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e);const n=window.wp.richText,o=window.wp.i18n,r=window.wp.blockEditor,i=window.wp.primitives,a=window.ReactJSXRuntime,s=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M14.7 11.3c1-.6 1.5-1.6 1.5-3 0-2.3-1.3-3.4-4-3.4H7v14h5.8c1.4 0 2.5-.3 3.3-1 .8-.7 1.2-1.7 1.2-2.9.1-1.9-.8-3.1-2.6-3.7zm-5.1-4h2.3c.6 0 1.1.1 1.4.4.3.3.5.7.5 1.2s-.2 1-.5 1.2c-.3.3-.8.4-1.4.4H9.6V7.3zm4.6 9c-.4.3-1 .4-1.7.4H9.6v-3.9h2.9c.7 0 1.3.2 1.7.5.4.3.6.8.6 1.5s-.2 1.2-.6 1.5z"})}),l="core/bold",c=(0,o.__)("Bold"),u={name:l,title:c,tagName:"strong",className:null,edit({isActive:t,value:e,onChange:o,onFocus:i}){function u(){o((0,n.toggleFormat)(e,{type:l,title:c}))}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"primary",character:"b",onUse:u}),(0,a.jsx)(r.RichTextToolbarButton,{name:"bold",icon:s,title:c,onClick:function(){o((0,n.toggleFormat)(e,{type:l})),i()},isActive:t,shortcutType:"primary",shortcutCharacter:"b"}),(0,a.jsx)(r.__unstableRichTextInputEvent,{inputType:"formatBold",onInput:u})]})}},h=(0,a.jsx)(i.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(i.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.1.1.1.3 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"})}),m="core/code",p=(0,o.__)("Inline code"),d={name:m,title:p,tagName:"code",className:null,__unstableInputRule(t){const{start:e,text:o}=t;if("`"!==o[e-1])return t;if(e-2<0)return t;const r=o.lastIndexOf("`",e-2);if(-1===r)return t;const i=r,a=e-2;return i===a?t:(t=(0,n.remove)(t,i,i+1),t=(0,n.remove)(t,a,a+1),t=(0,n.applyFormat)(t,{type:m},i,a))},edit({value:t,onChange:e,onFocus:o,isActive:i}){function s(){e((0,n.toggleFormat)(t,{type:m,title:p})),o()}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"access",character:"x",onUse:s}),(0,a.jsx)(r.RichTextToolbarButton,{icon:h,title:p,onClick:s,isActive:i,role:"menuitemcheckbox"})]})}},g=window.wp.components,x=window.wp.element,v=["image"],f="core/image",b=(0,o.__)("Inline image"),w={name:f,title:b,keywords:[(0,o.__)("photo"),(0,o.__)("media")],object:!0,tagName:"img",className:null,attributes:{className:"class",style:"style",url:"src",alt:"alt"},edit:function({value:t,onChange:e,onFocus:o,isObjectActive:i,activeObjectAttributes:s,contentRef:l}){return(0,a.jsxs)(r.MediaUploadCheck,{children:[(0,a.jsx)(r.MediaUpload,{allowedTypes:v,onSelect:({id:r,url:i,alt:a,width:s})=>{e((0,n.insertObject)(t,{type:f,attributes:{className:`wp-image-${r}`,style:`width: ${Math.min(s,150)}px;`,url:i,alt:a}})),o()},render:({open:t})=>(0,a.jsx)(r.RichTextToolbarButton,{icon:(0,a.jsx)(g.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(g.Path,{d:"M4 18.5h16V17H4v1.5zM16 13v1.5h4V13h-4zM5.1 15h7.8c.6 0 1.1-.5 1.1-1.1V6.1c0-.6-.5-1.1-1.1-1.1H5.1C4.5 5 4 5.5 4 6.1v7.8c0 .6.5 1.1 1.1 1.1zm.4-8.5h7V10l-1-1c-.3-.3-.8-.3-1 0l-1.6 1.5-1.2-.7c-.3-.2-.6-.2-.9 0l-1.3 1V6.5zm0 6.1l1.8-1.3 1.3.8c.3.2.7.2.9-.1l1.5-1.4 1.5 1.4v1.5h-7v-.9z"})}),title:b,onClick:t,isActive:i})}),i&&(0,a.jsx)(_,{value:t,onChange:e,activeObjectAttributes:s,contentRef:l})]})}};function _({value:t,onChange:e,activeObjectAttributes:r,contentRef:i}){const{style:s,alt:l}=r,c=s?.replace(/\D/g,""),[u,h]=(0,x.useState)(c),[m,p]=(0,x.useState)(l),d=u!==c||m!==l,v=(0,n.useAnchor)({editableContentElement:i.current,settings:w});return(0,a.jsx)(g.Popover,{placement:"bottom",focusOnMount:!1,anchor:v,className:"block-editor-format-toolbar__image-popover",children:(0,a.jsx)("form",{className:"block-editor-format-toolbar__image-container-content",onSubmit:n=>{const o=t.replacements.slice();o[t.start]={type:f,attributes:{...r,style:c?`width: ${u}px;`:"",alt:m}},e({...t,replacements:o}),n.preventDefault()},children:(0,a.jsxs)(g.__experimentalVStack,{spacing:4,children:[(0,a.jsx)(g.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,o.__)("Width"),value:u,min:1,onChange:t=>{h(t)}}),(0,a.jsx)(g.TextareaControl,{label:(0,o.__)("Alternative text"),__nextHasNoMarginBottom:!0,value:m,onChange:t=>{p(t)},help:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.ExternalLink,{href:(0,o.__)("https://www.w3.org/WAI/tutorials/images/decision-tree/"),children:(0,o.__)("Describe the purpose of the image.")}),(0,a.jsx)("br",{}),(0,o.__)("Leave empty if decorative.")]})}),(0,a.jsx)(g.__experimentalHStack,{justify:"right",children:(0,a.jsx)(g.Button,{disabled:!d,accessibleWhenDisabled:!0,variant:"primary",type:"submit",size:"compact",children:(0,o.__)("Apply")})})]})})})}const y=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M12.5 5L10 19h1.9l2.5-14z"})}),j="core/italic",k=(0,o.__)("Italic"),C={name:j,title:k,tagName:"em",className:null,edit({isActive:t,value:e,onChange:o,onFocus:i}){function s(){o((0,n.toggleFormat)(e,{type:j,title:k}))}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"primary",character:"i",onUse:s}),(0,a.jsx)(r.RichTextToolbarButton,{name:"italic",icon:y,title:k,onClick:function(){o((0,n.toggleFormat)(e,{type:j})),i()},isActive:t,shortcutType:"primary",shortcutCharacter:"i"}),(0,a.jsx)(r.__unstableRichTextInputEvent,{inputType:"formatItalic",onInput:s})]})}},S=window.wp.url,T=window.wp.htmlEntities,A=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M10 17.389H8.444A5.194 5.194 0 1 1 8.444 7H10v1.5H8.444a3.694 3.694 0 0 0 0 7.389H10v1.5ZM14 7h1.556a5.194 5.194 0 0 1 0 10.39H14v-1.5h1.556a3.694 3.694 0 0 0 0-7.39H14V7Zm-4.5 6h5v-1.5h-5V13Z"})}),F=window.wp.a11y,N=window.wp.data;function R(t){if(!t)return!1;const e=t.trim();if(!e)return!1;if(/^\S+:/.test(e)){const t=(0,S.getProtocol)(e);if(!(0,S.isValidProtocol)(t))return!1;if(t.startsWith("http")&&!/^https?:\/\/[^\/\s]/i.test(e))return!1;const n=(0,S.getAuthority)(e);if(!(0,S.isValidAuthority)(n))return!1;const o=(0,S.getPath)(e);if(o&&!(0,S.isValidPath)(o))return!1;const r=(0,S.getQueryString)(e);if(r&&!(0,S.isValidQueryString)(r))return!1;const i=(0,S.getFragment)(e);if(i&&!(0,S.isValidFragment)(i))return!1}return!(e.startsWith("#")&&!(0,S.isValidFragment)(e))}function V(t,e,n=t.start,o=t.end){const r={start:null,end:null},{formats:i}=t;let a,s;if(!i?.length)return r;const l=i.slice(),c=l[n]?.find((({type:t})=>t===e.type)),u=l[o]?.find((({type:t})=>t===e.type)),h=l[o-1]?.find((({type:t})=>t===e.type));if(c)a=c,s=n;else if(u)a=u,s=o;else{if(!h)return r;a=h,s=o-1}const m=l[s].indexOf(a),p=[l,s,a,m];return{start:n=(n=B(...p))<0?0:n,end:o=z(...p)}}function M(t,e,n,o,r){let i=e;const a={forwards:1,backwards:-1}[r]||1,s=-1*a;for(;t[i]&&t[i][o]===n;)i+=a;return i+=s,i}const P=(t,...e)=>(...n)=>t(...n,...e),B=P(M,"backwards"),z=P(M,"forwards"),L=[...r.__experimentalLinkControl.DEFAULT_LINK_SETTINGS,{id:"nofollow",title:(0,o.__)("Mark as nofollow")}];const I=function({isActive:t,activeAttributes:e,value:i,onChange:s,onFocusOutside:l,stopAddingLink:c,contentRef:u,focusOnMount:h}){const m=function(t,e){let o=t.start,r=t.end;if(e){const e=V(t,{type:"core/link"});o=e.start,r=e.end+1}return(0,n.slice)(t,o,r)}(i,t).text,{selectionChange:p}=(0,N.useDispatch)(r.store),{createPageEntity:d,userCanCreatePages:v,selectionStart:f}=(0,N.useSelect)((t=>{const{getSettings:e,getSelectionStart:n}=t(r.store),o=e();return{createPageEntity:o.__experimentalCreatePageEntity,userCanCreatePages:o.__experimentalUserCanCreatePages,selectionStart:n()}}),[]),b=(0,x.useMemo)((()=>({url:e.url,type:e.type,id:e.id,opensInNewTab:"_blank"===e.target,nofollow:e.rel?.includes("nofollow"),title:m})),[e.id,e.rel,e.target,e.type,e.url,m]),w=(0,n.useAnchor)({editableContentElement:u.current,settings:{...O,isActive:t}});return(0,a.jsx)(g.Popover,{anchor:w,animate:!1,onClose:c,onFocusOutside:l,placement:"bottom",offset:8,shift:!0,focusOnMount:h,constrainTabbing:!0,children:(0,a.jsx)(r.__experimentalLinkControl,{value:b,onChange:function(e){const r=b?.url,a=!r;e={...b,...e};const l=(0,S.prependHTTP)(e.url),u=function({url:t,type:e,id:n,opensInNewWindow:o,nofollow:r}){const i={type:"core/link",attributes:{url:t}};return e&&(i.attributes.type=e),n&&(i.attributes.id=n),o&&(i.attributes.target="_blank",i.attributes.rel=i.attributes.rel?i.attributes.rel+" noreferrer noopener":"noreferrer noopener"),r&&(i.attributes.rel=i.attributes.rel?i.attributes.rel+" nofollow":"nofollow"),i}({url:l,type:e.type,id:void 0!==e.id&&null!==e.id?String(e.id):void 0,opensInNewWindow:e.opensInNewTab,nofollow:e.nofollow}),h=e.title||l;let d;if((0,n.isCollapsed)(i)&&!t){const t=(0,n.insert)(i,h);return d=(0,n.applyFormat)(t,u,i.start,i.start+h.length),s(d),c(),void p({clientId:f.clientId,identifier:f.attributeKey,start:i.start+h.length+1})}if(h===m)d=(0,n.applyFormat)(i,u);else{d=(0,n.create)({text:h}),d=(0,n.applyFormat)(d,u,0,h.length);const t=V(i,{type:"core/link"}),[e,o]=(0,n.split)(i,t.start,t.start),r=(0,n.replace)(o,m,d);d=(0,n.concat)(e,r)}s(d),a||c(),R(l)?t?(0,F.speak)((0,o.__)("Link edited."),"assertive"):(0,F.speak)((0,o.__)("Link inserted."),"assertive"):(0,F.speak)((0,o.__)("Warning: the link has been inserted but may have errors. Please test it."),"assertive")},onRemove:function(){const t=(0,n.removeFormat)(i,"core/link");s(t),c(),(0,F.speak)((0,o.__)("Link removed."),"assertive")},hasRichPreviews:!0,createSuggestion:d&&async function(t){const e=await d({title:t,status:"draft"});return{id:e.id,type:e.type,title:e.title.rendered,url:e.link,kind:"post-type"}},withCreateSuggestion:v,createSuggestionButtonText:function(t){return(0,x.createInterpolateElement)((0,o.sprintf)((0,o.__)("Create page: <mark>%s</mark>"),t),{mark:(0,a.jsx)("mark",{})})},hasTextControl:!0,settings:L,showInitialSuggestions:!0,suggestionsQuery:{initialSuggestionsSearchOptions:{type:"post",subtype:"page",perPage:20}}})})},E="core/link",H=(0,o.__)("Link");const O={name:E,title:H,tagName:"a",className:null,attributes:{url:"href",type:"data-type",id:"data-id",_id:"id",target:"target",rel:"rel"},__unstablePasteRule(t,{html:e,plainText:o}){const r=(e||o).replace(/<[^>]+>/g,"").trim();if(!(0,S.isURL)(r)||!/^https?:/.test(r))return t;window.console.log("Created link:\n\n",r);const i={type:E,attributes:{url:(0,T.decodeEntities)(r)}};return(0,n.isCollapsed)(t)?(0,n.insert)(t,(0,n.applyFormat)((0,n.create)({text:o}),i,0,o.length)):(0,n.applyFormat)(t,i)},edit:function({isActive:t,activeAttributes:e,value:i,onChange:s,onFocus:l,contentRef:c}){const[u,h]=(0,x.useState)(!1),[m,p]=(0,x.useState)(null);function d(e){const o=(0,n.getTextContent)((0,n.slice)(i));!t&&o&&(0,S.isURL)(o)&&R(o)?s((0,n.applyFormat)(i,{type:E,attributes:{url:o}})):!t&&o&&(0,S.isEmail)(o)?s((0,n.applyFormat)(i,{type:E,attributes:{url:`mailto:${o}`}})):!t&&o&&(0,S.isPhoneNumber)(o)?s((0,n.applyFormat)(i,{type:E,attributes:{url:`tel:${o.replace(/\D/g,"")}`}})):(e&&p({el:e,action:null}),h(!0))}(0,x.useEffect)((()=>{t||h(!1)}),[t]),(0,x.useLayoutEffect)((()=>{const e=c.current;if(e)return e.addEventListener("click",n),()=>{e.removeEventListener("click",n)};function n(e){const n=e.target.closest("[contenteditable] a");n&&t&&(h(!0),p({el:n,action:"click"}))}}),[c,t]);const g=!("A"===m?.el?.tagName&&"click"===m?.action);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"primary",character:"k",onUse:d}),(0,a.jsx)(r.RichTextShortcut,{type:"primaryShift",character:"k",onUse:function(){s((0,n.removeFormat)(i,E)),(0,F.speak)((0,o.__)("Link removed."),"assertive")}}),(0,a.jsx)(r.RichTextToolbarButton,{name:"link",icon:A,title:t?(0,o.__)("Link"):H,onClick:t=>{d(t.currentTarget)},isActive:t||u,shortcutType:"primary",shortcutCharacter:"k","aria-haspopup":"true","aria-expanded":u}),u&&(0,a.jsx)(I,{stopAddingLink:function(){h(!1),"BUTTON"===m?.el?.tagName?m.el.focus():l(),p(null)},onFocusOutside:function(){h(!1),p(null)},isActive:t,activeAttributes:e,value:i,onChange:s,contentRef:c,focusOnMount:!!g&&"firstElement"})]})}},U=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M9.1 9v-.5c0-.6.2-1.1.7-1.4.5-.3 1.2-.5 2-.5.7 0 1.4.1 2.1.3.7.2 1.4.5 2.1.9l.2-1.9c-.6-.3-1.2-.5-1.9-.7-.8-.1-1.6-.2-2.4-.2-1.5 0-2.7.3-3.6 1-.8.7-1.2 1.5-1.2 2.6V9h2zM20 12H4v1h8.3c.3.1.6.2.8.3.5.2.9.5 1.1.8.3.3.4.7.4 1.2 0 .7-.2 1.1-.8 1.5-.5.3-1.2.5-2.1.5-.8 0-1.6-.1-2.4-.3-.8-.2-1.5-.5-2.2-.8L7 18.1c.5.2 1.2.4 2 .6.8.2 1.6.3 2.4.3 1.7 0 3-.3 3.9-1 .9-.7 1.3-1.6 1.3-2.8 0-.9-.2-1.7-.7-2.2H20v-1z"})}),G="core/strikethrough",D=(0,o.__)("Strikethrough"),W={name:G,title:D,tagName:"s",className:null,edit({isActive:t,value:e,onChange:o,onFocus:i}){function s(){o((0,n.toggleFormat)(e,{type:G,title:D})),i()}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"access",character:"d",onUse:s}),(0,a.jsx)(r.RichTextToolbarButton,{icon:U,title:D,onClick:s,isActive:t,role:"menuitemcheckbox"})]})}},Z="core/underline",$=(0,o.__)("Underline"),K={name:Z,title:$,tagName:"span",className:null,attributes:{style:"style"},edit({value:t,onChange:e}){const o=()=>{e((0,n.toggleFormat)(t,{type:Z,attributes:{style:"text-decoration: underline;"},title:$}))};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextShortcut,{type:"primary",character:"u",onUse:o}),(0,a.jsx)(r.__unstableRichTextInputEvent,{inputType:"formatUnderline",onInput:o})]})}};const Q=(0,x.forwardRef)((function({icon:t,size:e=24,...n},o){return(0,x.cloneElement)(t,{width:e,height:e,...n,ref:o})})),J=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M12.9 6h-2l-4 11h1.9l1.1-3h4.2l1.1 3h1.9L12.9 6zm-2.5 6.5l1.5-4.9 1.7 4.9h-3.2z"})}),X=(0,a.jsx)(i.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(i.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),q=window.wp.privateApis,{lock:Y,unlock:tt}=(0,q.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/format-library"),{Tabs:et}=tt(g.privateApis),nt=[{name:"color",title:(0,o.__)("Text")},{name:"backgroundColor",title:(0,o.__)("Background")}];function ot(t=""){return t.split(";").reduce(((t,e)=>{if(e){const[n,o]=e.split(":");"color"===n&&(t.color=o),"background-color"===n&&o!==lt&&(t.backgroundColor=o)}return t}),{})}function rt(t="",e){return t.split(" ").reduce(((t,n)=>{if(n.startsWith("has-")&&n.endsWith("-color")){const o=n.replace(/^has-/,"").replace(/-color$/,""),i=(0,r.getColorObjectByAttributeValues)(e,o);t.color=i.color}return t}),{})}function it(t,e,o){const r=(0,n.getActiveFormat)(t,e);return r?{...ot(r.attributes.style),...rt(r.attributes.class,o)}:{}}function at({name:t,property:e,value:o,onChange:i}){const s=(0,N.useSelect)((t=>{var e;const{getSettings:n}=t(r.store);return null!==(e=n().colors)&&void 0!==e?e:[]}),[]),l=(0,x.useMemo)((()=>it(o,t,s)),[t,o,s]);return(0,a.jsx)(r.ColorPalette,{value:l[e],onChange:a=>{i(function(t,e,o,i){const{color:a,backgroundColor:s}={...it(t,e,o),...i};if(!a&&!s)return(0,n.removeFormat)(t,e);const l=[],c=[],u={};if(s?l.push(["background-color",s].join(":")):l.push(["background-color",lt].join(":")),a){const t=(0,r.getColorObjectByColorValue)(o,a);t?c.push((0,r.getColorClassName)("color",t.slug)):l.push(["color",a].join(":"))}return l.length&&(u.style=l.join(";")),c.length&&(u.class=c.join(" ")),(0,n.applyFormat)(t,{type:e,attributes:u})}(o,t,s,{[e]:a}))}})}function st({name:t,value:e,onChange:o,onClose:r,contentRef:i,isActive:s}){const l=(0,n.useAnchor)({editableContentElement:i.current,settings:{...pt,isActive:s}});return(0,a.jsx)(g.Popover,{onClose:r,className:"format-library__inline-color-popover",anchor:l,children:(0,a.jsxs)(et,{children:[(0,a.jsx)(et.TabList,{children:nt.map((t=>(0,a.jsx)(et.Tab,{tabId:t.name,children:t.title},t.name)))}),nt.map((n=>(0,a.jsx)(et.TabPanel,{tabId:n.name,focusable:!1,children:(0,a.jsx)(at,{name:t,property:n.name,value:e,onChange:o})},n.name)))]})})}const lt="rgba(0, 0, 0, 0)",ct="core/text-color",ut=(0,o.__)("Highlight"),ht=[];function mt(t,e){const{ownerDocument:n}=t,{defaultView:o}=n,r=o.getComputedStyle(t).getPropertyValue(e);return"background-color"===e&&r===lt&&t.parentElement?mt(t.parentElement,e):r}const pt={name:ct,title:ut,tagName:"mark",className:"has-inline-color",attributes:{style:"style",class:"class"},edit:function({value:t,onChange:e,isActive:o,activeAttributes:i,contentRef:s}){const[l,c=ht]=(0,r.useSettings)("color.custom","color.palette"),[u,h]=(0,x.useState)(!1),m=(0,x.useMemo)((()=>function(t,{color:e,backgroundColor:n}){if(e||n)return{color:e||mt(t,"color"),backgroundColor:n===lt?mt(t,"background-color"):n}}(s.current,it(t,ct,c))),[s,t,c]),p=c.length||!l;return p||o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextToolbarButton,{className:"format-library-text-color-button",isActive:o,icon:(0,a.jsx)(Q,{icon:Object.keys(i).length?J:X,style:m}),title:ut,onClick:p?()=>h(!0):()=>e((0,n.removeFormat)(t,ct)),role:"menuitemcheckbox"}),u&&(0,a.jsx)(st,{name:ct,onClose:()=>h(!1),activeAttributes:i,value:t,onChange:e,contentRef:s,isActive:o})]}):null}},dt=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M16.9 18.3l.8-1.2c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.1-.3-.4-.5-.6-.7-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.2 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3L15 19.4h4.3v-1.2h-2.4zM14.1 7.2h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"})}),gt="core/subscript",xt=(0,o.__)("Subscript"),vt={name:gt,title:xt,tagName:"sub",className:null,edit:({isActive:t,value:e,onChange:o,onFocus:i})=>(0,a.jsx)(r.RichTextToolbarButton,{icon:dt,title:xt,onClick:function(){o((0,n.toggleFormat)(e,{type:gt,title:xt})),i()},isActive:t,role:"menuitemcheckbox"})},ft=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M16.9 10.3l.8-1.3c.4-.6.7-1.2.9-1.6.2-.4.3-.8.3-1.2 0-.3-.1-.7-.2-1-.2-.2-.4-.4-.7-.6-.3-.2-.6-.3-1-.3s-.8.1-1.1.2c-.3.1-.7.3-1 .6l.1 1.3c.3-.3.5-.5.8-.6s.6-.2.9-.2c.3 0 .*******.*******.2.7 0 .3-.1.5-.2.8-.1.3-.4.7-.8 1.3l-1.8 2.8h4.3v-1.2h-2.2zm-2.8-3.1h-2L9.5 11 6.9 7.2h-2l3.6 5.3L4.7 18h2l2.7-4 2.7 4h2l-3.8-5.5 3.8-5.3z"})}),bt="core/superscript",wt=(0,o.__)("Superscript"),_t={name:bt,title:wt,tagName:"sup",className:null,edit:({isActive:t,value:e,onChange:o,onFocus:i})=>(0,a.jsx)(r.RichTextToolbarButton,{icon:ft,title:wt,onClick:function(){o((0,n.toggleFormat)(e,{type:bt,title:wt})),i()},isActive:t,role:"menuitemcheckbox"})},yt=(0,a.jsx)(i.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)(i.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})}),jt="core/keyboard",kt=(0,o.__)("Keyboard input"),Ct={name:jt,title:kt,tagName:"kbd",className:null,edit:({isActive:t,value:e,onChange:o,onFocus:i})=>(0,a.jsx)(r.RichTextToolbarButton,{icon:yt,title:kt,onClick:function(){o((0,n.toggleFormat)(e,{type:jt,title:kt})),i()},isActive:t,role:"menuitemcheckbox"})},St=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zM3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 8.75a1.5 1.5 0 01.167 2.99c-.465.052-.917.44-.917 1.01V14h1.5v-.845A3 3 0 109 10.25h1.5a1.5 1.5 0 011.5-1.5zM11.25 15v1.5h1.5V15h-1.5z"})}),Tt="core/unknown",At=(0,o.__)("Clear Unknown Formatting");const Ft={name:Tt,title:At,tagName:"*",className:null,edit({isActive:t,value:e,onChange:o,onFocus:i}){if(!t&&!function(t){return!(0,n.isCollapsed)(t)&&(0,n.slice)(t).formats.some((t=>t.some((t=>t.type===Tt))))}(e))return null;return(0,a.jsx)(r.RichTextToolbarButton,{name:"unknown",icon:St,title:At,onClick:function(){o((0,n.removeFormat)(e,Tt)),i()},isActive:!0})}},Nt=(0,a.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(i.Path,{d:"M17.5 10h-1.7l-3.7 10.5h1.7l.9-2.6h3.9l.9 2.6h1.7L17.5 10zm-2.2 6.3 1.4-4 1.4 4h-2.8zm-4.8-3.8c1.6-1.8 2.9-3.6 3.7-5.7H16V5.2h-5.8V3H8.8v2.2H3v1.5h9.6c-.7 1.6-1.8 3.1-3.1 4.6C8.6 10.2 7.8 9 7.2 8H5.6c.6 1.4 1.7 2.9 2.9 4.4l-2.4 2.4c-.3.4-.7.8-1.1 1.2l1 1 1.2-1.2c.8-.8 1.6-1.5 2.3-2.3.8.9 1.7 1.7 2.5 2.5l.6-1.5c-.7-.6-1.4-1.3-2.1-2z"})}),Rt="core/language",Vt=(0,o.__)("Language"),Mt={name:Rt,tagName:"bdo",className:null,edit:function({isActive:t,value:e,onChange:o,contentRef:i}){const[s,l]=(0,x.useState)(!1),c=()=>{l((t=>!t))};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.RichTextToolbarButton,{icon:Nt,label:Vt,title:Vt,onClick:()=>{t?o((0,n.removeFormat)(e,Rt)):c()},isActive:t,role:"menuitemcheckbox"}),s&&(0,a.jsx)(Pt,{value:e,onChange:o,onClose:c,contentRef:i})]})},title:Vt};function Pt({value:t,contentRef:e,onChange:r,onClose:i}){const s=(0,n.useAnchor)({editableContentElement:e.current,settings:Mt}),[l,c]=(0,x.useState)(""),[u,h]=(0,x.useState)("ltr");return(0,a.jsx)(g.Popover,{className:"block-editor-format-toolbar__language-popover",anchor:s,onClose:i,children:(0,a.jsxs)(g.__experimentalVStack,{as:"form",spacing:4,className:"block-editor-format-toolbar__language-container-content",onSubmit:e=>{e.preventDefault(),r((0,n.applyFormat)(t,{type:Rt,attributes:{lang:l,dir:u}})),i()},children:[(0,a.jsx)(g.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:Vt,value:l,onChange:t=>c(t),help:(0,o.__)('A valid language attribute, like "en" or "fr".')}),(0,a.jsx)(g.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,o.__)("Text direction"),value:u,options:[{label:(0,o.__)("Left to right"),value:"ltr"},{label:(0,o.__)("Right to left"),value:"rtl"}],onChange:t=>h(t)}),(0,a.jsx)(g.__experimentalHStack,{alignment:"right",children:(0,a.jsx)(g.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",text:(0,o.__)("Apply")})})]})})}const Bt=(0,o.__)("Non breaking space");[u,d,w,C,O,W,K,pt,vt,_t,Ct,Ft,Mt,{name:"core/non-breaking-space",title:Bt,tagName:"nbsp",className:null,edit:({value:t,onChange:e})=>(0,a.jsx)(r.RichTextShortcut,{type:"primaryShift",character:" ",onUse:function(){e((0,n.insert)(t," "))}})}].forEach((({name:t,...e})=>(0,n.registerFormatType)(t,e))),(window.wp=window.wp||{}).formatLibrary=e})();