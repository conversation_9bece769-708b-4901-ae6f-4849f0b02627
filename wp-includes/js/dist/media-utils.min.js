/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{MediaUpload:()=>p,transformAttachment:()=>y,uploadMedia:()=>S,validateFileSize:()=>b,validateMimeType:()=>w,validateMimeTypeForUser:()=>g});const i=window.wp.element,o=window.wp.i18n,a=[],s=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Select.extend({featuredImageToolbar(t){this.createSelectToolbar(t,{text:e.media.view.l10n.setFeaturedImage,state:this.options.state})},editState(){const t=this.state("featured-image").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.FeaturedImage,new e.media.controller.EditImage({model:this.options.editImage})])}})},r=()=>{const{wp:e}=window;return e.media.view.MediaFrame.Post.extend({galleryToolbar(){const t=this.state().get("editing");this.toolbar.set(new e.media.view.Toolbar({controller:this,items:{insert:{style:"primary",text:t?e.media.view.l10n.updateGallery:e.media.view.l10n.insertGallery,priority:80,requires:{library:!0},click(){const e=this.controller,t=e.state();e.close(),t.trigger("update",t.get("library")),e.setState(e.options.state),e.reset()}}}}))},editState(){const t=this.state("gallery").get("selection"),i=new e.media.view.EditImage({model:t.single(),controller:this}).render();this.content.set(i),i.loadEditor()},createStates:function(){this.on("toolbar:create:main-gallery",this.galleryToolbar,this),this.on("content:render:edit-image",this.editState,this),this.states.add([new e.media.controller.Library({id:"gallery",title:e.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:e.media.query({type:"image",...this.options.library})}),new e.media.controller.EditImage({model:this.options.editImage}),new e.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new e.media.controller.GalleryAdd])}})},n=e=>["sizes","mime","type","subtype","id","url","alt","link","caption"].reduce(((t,i)=>(e?.hasOwnProperty(i)&&(t[i]=e[i]),t)),{}),l=e=>{const{wp:t}=window;return t.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})};class d extends i.Component{constructor(){super(...arguments),this.openModal=this.openModal.bind(this),this.onOpen=this.onOpen.bind(this),this.onSelect=this.onSelect.bind(this),this.onUpdate=this.onUpdate.bind(this),this.onClose=this.onClose.bind(this)}initializeListeners(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}buildAndSetGalleryFrame(){const{addToGallery:e=!1,allowedTypes:t,multiple:i=!1,value:o=a}=this.props;if(o===this.lastGalleryValue)return;const{wp:s}=window;let n;this.lastGalleryValue=o,this.frame&&this.frame.remove(),n=e?"gallery-library":o&&o.length?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=r());const d=l(o),p=new s.media.model.Selection(d.models,{props:d.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:t,state:n,multiple:i,selection:p,editing:!(!o||!o.length)}),s.media.frame=this.frame,this.initializeListeners()}buildAndSetFeatureImageFrame(){const{wp:e}=window,{value:t,multiple:i,allowedTypes:o}=this.props,a=s(),r=l(t),n=new e.media.model.Selection(r.models,{props:r.props.toJSON()});this.frame=new a({mimeType:o,state:"featured-image",multiple:i,selection:n,editing:t}),e.media.frame=this.frame,e.media.view.settings.post={...e.media.view.settings.post,featuredImageId:t||-1}}componentWillUnmount(){this.frame?.remove()}onUpdate(e){const{onSelect:t,multiple:i=!1}=this.props,o=this.frame.state(),a=e||o.get("selection");a&&a.models.length&&t(i?a.models.map((e=>n(e.toJSON()))):n(a.models[0].toJSON()))}onSelect(){const{onSelect:e,multiple:t=!1}=this.props,i=this.frame.state().get("selection").toJSON();e(t?i:i[0])}onOpen(){const{wp:e}=window,{value:t}=this.props;this.updateCollection(),this.props.mode&&this.frame.content.mode(this.props.mode);if(!(Array.isArray(t)?!!t?.length:!!t))return;const i=this.props.gallery,o=this.frame.state().get("selection"),a=Array.isArray(t)?t:[t];i||a.forEach((t=>{o.add(e.media.attachment(t))}));const s=l(a);s.more().done((function(){i&&s?.models?.length&&o.add(s.models)}))}onClose(){const{onClose:e}=this.props;e&&e(),this.frame.detach()}updateCollection(){const e=this.frame.content.get();if(e&&e.collection){const t=e.collection;t.toArray().forEach((e=>e.trigger("destroy",e))),t.mirroring._hasMore=!0,t.more()}}openModal(){const{allowedTypes:e,gallery:t=!1,unstableFeaturedImageFlow:i=!1,modalClass:a,multiple:s=!1,title:r=(0,o.__)("Select or Upload Media")}=this.props,{wp:n}=window;if(t)this.buildAndSetGalleryFrame();else{const t={title:r,multiple:s};e&&(t.library={type:e}),this.frame=n.media(t)}a&&this.frame.$el.addClass(a),i&&this.buildAndSetFeatureImageFrame(),this.initializeListeners(),this.frame.open()}render(){return this.props.render({open:this.openModal})}}const p=d,m=window.wp.blob,c=window.wp.apiFetch;var h=e.n(c);function u(e,t,i){if(function(e){return null!==e&&"object"==typeof e&&Object.getPrototypeOf(e)===Object.prototype}(i))for(const[o,a]of Object.entries(i))u(e,`${t}[${o}]`,a);else void 0!==i&&e.append(t,String(i))}function y(e){var t;const{alt_text:i,source_url:o,...a}=e;return{...a,alt:e.alt_text,caption:null!==(t=e.caption?.raw)&&void 0!==t?t:"",title:e.title.raw,url:e.source_url,poster:e._embedded?.["wp:featuredmedia"]?.[0]?.source_url||void 0}}class f extends Error{constructor({code:e,message:t,file:i,cause:o}){super(t,{cause:o}),Object.setPrototypeOf(this,new.target.prototype),this.code=e,this.file=i}}function w(e,t){if(!t)return;const i=t.some((t=>t.includes("/")?t===e.type:e.type.startsWith(`${t}/`)));if(e.type&&!i)throw new f({code:"MIME_TYPE_NOT_SUPPORTED",message:(0,o.sprintf)((0,o.__)("%s: Sorry, this file type is not supported here."),e.name),file:e})}function g(e,t){const i=(a=t)?Object.entries(a).flatMap((([e,t])=>{const[i]=t.split("/");return[t,...e.split("|").map((e=>`${i}/${e}`))]})):null;var a;if(!i)return;const s=i.includes(e.type);if(e.type&&!s)throw new f({code:"MIME_TYPE_NOT_ALLOWED_FOR_USER",message:(0,o.sprintf)((0,o.__)("%s: Sorry, you are not allowed to upload this file type."),e.name),file:e})}function b(e,t){if(e.size<=0)throw new f({code:"EMPTY_FILE",message:(0,o.sprintf)((0,o.__)("%s: This file is empty."),e.name),file:e});if(t&&e.size>t)throw new f({code:"SIZE_ABOVE_LIMIT",message:(0,o.sprintf)((0,o.__)("%s: This file exceeds the maximum upload size for this site."),e.name),file:e})}function S({wpAllowedMimeTypes:e,allowedTypes:t,additionalData:i={},filesList:a,maxUploadFileSize:s,onError:r,onFileChange:n,signal:l}){const d=[],p=[],c=(e,t)=>{p[e]?.url&&(0,m.revokeBlobURL)(p[e].url),p[e]=t,n?.(p.filter((e=>null!==e)))};for(const i of a){try{g(i,e)}catch(e){r?.(e);continue}try{w(i,t)}catch(e){r?.(e);continue}try{b(i,s)}catch(e){r?.(e);continue}d.push(i),p.push({url:(0,m.createBlobURL)(i)}),n?.(p)}d.map((async(e,t)=>{try{const o=await async function(e,t={},i){const o=new FormData;o.append("file",e,e.name||e.type.replace("/","."));for(const[e,i]of Object.entries(t))u(o,e,i);return y(await h()({path:"/wp/v2/media?_embed=wp:featuredmedia",body:o,method:"POST",signal:i}))}(e,i,l);c(t,o)}catch(i){let a;c(t,null),a=i instanceof Error?i.message:(0,o.sprintf)((0,o.__)("Error while uploading file %s to the media library."),e.name),r?.(new f({code:"GENERAL",message:a,file:e,cause:i instanceof Error?i:void 0}))}}))}(window.wp=window.wp||{}).mediaUtils=t})();