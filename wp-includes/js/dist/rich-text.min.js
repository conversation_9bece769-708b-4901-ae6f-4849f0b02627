/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{RichTextData:()=>j,__experimentalRichText:()=>Xe,__unstableCreateElement:()=>y,__unstableToDom:()=>be,__unstableUseRichText:()=>Be,applyFormat:()=>h,concat:()=>Y,create:()=>V,getActiveFormat:()=>G,getActiveFormats:()=>T,getActiveObject:()=>Z,getTextContent:()=>H,insert:()=>oe,insertObject:()=>ie,isCollapsed:()=>J,isEmpty:()=>Q,join:()=>ee,registerFormatType:()=>te,remove:()=>ae,removeFormat:()=>ne,replace:()=>se,slice:()=>ce,split:()=>le,store:()=>f,toHTMLString:()=>_,toggleFormat:()=>Le,unregisterFormatType:()=>Ce,useAnchor:()=>De,useAnchorRef:()=>Se});var n={};e.r(n),e.d(n,{getFormatType:()=>i,getFormatTypeForBareElement:()=>c,getFormatTypeForClassName:()=>l,getFormatTypes:()=>s});var r={};e.r(r),e.d(r,{addFormatTypes:()=>u,removeFormatTypes:()=>d});const o=window.wp.data;const a=(0,o.combineReducers)({formatTypes:function(e={},t){switch(t.type){case"ADD_FORMAT_TYPES":return{...e,...t.formatTypes.reduce(((e,t)=>({...e,[t.name]:t})),{})};case"REMOVE_FORMAT_TYPES":return Object.fromEntries(Object.entries(e).filter((([e])=>!t.names.includes(e))))}return e}}),s=(0,o.createSelector)((e=>Object.values(e.formatTypes)),(e=>[e.formatTypes]));function i(e,t){return e.formatTypes[t]}function c(e,t){const n=s(e);return n.find((({className:e,tagName:n})=>null===e&&t===n))||n.find((({className:e,tagName:t})=>null===e&&"*"===t))}function l(e,t){return s(e).find((({className:e})=>null!==e&&` ${t} `.indexOf(` ${e} `)>=0))}function u(e){return{type:"ADD_FORMAT_TYPES",formatTypes:Array.isArray(e)?e:[e]}}function d(e){return{type:"REMOVE_FORMAT_TYPES",names:Array.isArray(e)?e:[e]}}const f=(0,o.createReduxStore)("core/rich-text",{reducer:a,selectors:n,actions:r});function m(e,t){if(e===t)return!0;if(!e||!t)return!1;if(e.type!==t.type)return!1;const n=e.attributes,r=t.attributes;if(n===r)return!0;if(!n||!r)return!1;const o=Object.keys(n),a=Object.keys(r);if(o.length!==a.length)return!1;const s=o.length;for(let e=0;e<s;e++){const t=o[e];if(n[t]!==r[t])return!1}return!0}function p(e){const t=e.formats.slice();return t.forEach(((e,n)=>{const r=t[n-1];if(r){const o=e.slice();o.forEach(((e,t)=>{const n=r[t];m(e,n)&&(o[t]=n)})),t[n]=o}})),{...e,formats:t}}function g(e,t,n){return(e=e.slice())[t]=n,e}function h(e,t,n=e.start,r=e.end){const{formats:o,activeFormats:a}=e,s=o.slice();if(n===r){const e=s[n]?.find((({type:e})=>e===t.type));if(e){const o=s[n].indexOf(e);for(;s[n]&&s[n][o]===e;)s[n]=g(s[n],o,t),n--;for(r++;s[r]&&s[r][o]===e;)s[r]=g(s[r],o,t),r++}}else{let e=1/0;for(let o=n;o<r;o++)if(s[o]){s[o]=s[o].filter((({type:e})=>e!==t.type));const n=s[o].length;n<e&&(e=n)}else s[o]=[],e=0;for(let o=n;o<r;o++)s[o].splice(e,0,t)}return p({...e,formats:s,activeFormats:[...a?.filter((({type:e})=>e!==t.type))||[],t]})}function y({implementation:e},t){return y.body||(y.body=e.createHTMLDocument("").body),y.body.innerHTML=t,y.body}(0,o.register)(f);const v="￼",E="\ufeff",b=window.wp.escapeHtml;function T(e,t=[]){const{formats:n,start:r,end:o,activeFormats:a}=e;if(void 0===r)return t;if(r===o){if(a)return a;const e=n[r-1]||t,o=n[r]||t;return e.length<o.length?e:o}if(!n[r])return t;const s=n.slice(r,o),i=[...s[0]];let c=s.length;for(;c--;){const e=s[c];if(!e)return t;let n=i.length;for(;n--;){const t=i[n];e.find((e=>m(t,e)))||i.splice(n,1)}if(0===i.length)return t}return i||t}function x(e){return(0,o.select)(f).getFormatType(e)}function w(e,t){if(t)return e;const n={};for(const t in e){let r=t;t.startsWith("data-disable-rich-text-")&&(r=t.slice(23)),n[r]=e[t]}return n}function N({type:e,tagName:t,attributes:n,unregisteredAttributes:r,object:o,boundaryClass:a,isEditableTree:s}){const i=x(e);let c={};if(a&&s&&(c["data-rich-text-format-boundary"]="true"),!i)return n&&(c={...n,...c}),{type:e,attributes:w(c,s),object:o};c={...r,...c};for(const e in n){const t=!!i.attributes&&i.attributes[e];t?c[t]=n[e]:c[e]=n[e]}return i.className&&(c.class?c.class=`${i.className} ${c.class}`:c.class=i.className),s&&!1===i.contentEditable&&(c.contenteditable="false"),{type:t||i.tagName,object:i.object,attributes:w(c,s)}}function L(e,t,n){do{if(e[n]!==t[n])return!1}while(n--);return!0}function C({value:e,preserveWhiteSpace:t,createEmpty:n,append:r,getLastChild:o,getParent:a,isText:s,getText:i,remove:c,appendText:l,onStartIndex:u,onEndIndex:d,isEditableTree:f,placeholder:m}){const{formats:p,replacements:g,text:h,start:y,end:b}=e,w=p.length+1,C=n(),_=T(e),F=_[_.length-1];let O,S;r(C,"");for(let e=0;e<w;e++){const n=h.charAt(e),T=f&&(!S||"\n"===S),w=p[e];let _=o(C);if(w&&w.forEach(((e,t)=>{if(_&&O&&L(w,O,t))return void(_=o(_));const{type:n,tagName:l,attributes:u,unregisteredAttributes:d}=e,m=f&&e===F,p=a(_),g=r(p,N({type:n,tagName:l,attributes:u,unregisteredAttributes:d,boundaryClass:m,isEditableTree:f}));s(_)&&0===i(_).length&&c(_),_=r(g,"")})),0===e&&(u&&0===y&&u(C,_),d&&0===b&&d(C,_)),n===v){const t=g[e];if(!t)continue;const{type:n,attributes:o,innerHTML:s}=t,i=x(n);f||"script"!==n?!1===i?.contentEditable?(_=r(a(_),N({...t,isEditableTree:f,boundaryClass:y===e&&b===e+1})),s&&r(_,{html:s})):_=r(a(_),N({...t,object:!0,isEditableTree:f})):(_=r(a(_),N({type:"script",isEditableTree:f})),r(_,{html:decodeURIComponent(o["data-rich-text-script"])})),_=r(a(_),"")}else t||"\n"!==n?s(_)?l(_,n):_=r(a(_),n):(_=r(a(_),{type:"br",attributes:f?{"data-rich-text-line-break":"true"}:void 0,object:!0}),_=r(a(_),""));u&&y===e+1&&u(C,_),d&&b===e+1&&d(C,_),T&&e===h.length&&(r(a(_),E),m&&0===h.length&&r(a(_),{type:"span",attributes:{"data-rich-text-placeholder":m,style:"pointer-events:none;user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;"}})),O=w,S=n}return C}function _({value:e,preserveWhiteSpace:t}){return $(C({value:e,preserveWhiteSpace:t,createEmpty:F,append:S,getLastChild:O,getParent:R,isText:D,getText:M,remove:k,appendText:A}).children)}function F(){return{}}function O({children:e}){return e&&e[e.length-1]}function S(e,t){return"string"==typeof t&&(t={text:t}),t.parent=e,e.children=e.children||[],e.children.push(t),t}function A(e,t){e.text+=t}function R({parent:e}){return e}function D({text:e}){return"string"==typeof e}function M({text:e}){return e}function k(e){const t=e.parent.children.indexOf(e);return-1!==t&&e.parent.children.splice(t,1),e}function $(e=[]){return e.map((e=>void 0!==e.html?e.html:void 0===e.text?function({type:e,attributes:t,object:n,children:r}){let o="";for(const e in t)(0,b.isValidAttributeName)(e)&&(o+=` ${e}="${(0,b.escapeAttribute)(t[e])}"`);return n?`<${e}${o}>`:`<${e}${o}>${$(r)}</${e}>`}(e):(0,b.escapeEditableHTML)(e.text))).join("")}function H({text:e}){return e.replace(v,"")}function P({tagName:e,attributes:t}){let n;if(t&&t.class&&(n=(0,o.select)(f).getFormatTypeForClassName(t.class),n&&(t.class=` ${t.class} `.replace(` ${n.className} `," ").trim(),t.class||delete t.class)),n||(n=(0,o.select)(f).getFormatTypeForBareElement(e)),!n)return t?{type:e,attributes:t}:{type:e};if(n.__experimentalCreatePrepareEditableTree&&!n.__experimentalCreateOnChangeEditableValue)return null;if(!t)return{formatType:n,type:n.name,tagName:e};const r={},a={},s={...t};for(const e in n.attributes){const t=n.attributes[e];r[e]=s[t],delete s[t],void 0===r[e]&&delete r[e]}for(const e in s)a[e]=t[e];return!1===n.contentEditable&&delete a.contenteditable,{formatType:n,type:n.name,tagName:e,attributes:r,unregisteredAttributes:a}}class j{#e;static empty(){return new j}static fromPlainText(e){return new j(V({text:e}))}static fromHTMLString(e){return new j(V({html:e}))}static fromHTMLElement(e,t={}){const{preserveWhiteSpace:n=!1}=t,r=n?e:z(e),o=new j(V({element:r}));return Object.defineProperty(o,"originalHTML",{value:e.innerHTML}),o}constructor(e={formats:[],replacements:[],text:""}){this.#e=e}toPlainText(){return H(this.#e)}toHTMLString({preserveWhiteSpace:e}={}){return this.originalHTML||_({value:this.#e,preserveWhiteSpace:e})}valueOf(){return this.toHTMLString()}toString(){return this.toHTMLString()}toJSON(){return this.toHTMLString()}get length(){return this.text.length}get formats(){return this.#e.formats}get replacements(){return this.#e.replacements}get text(){return this.#e.text}}for(const e of Object.getOwnPropertyNames(String.prototype))j.prototype.hasOwnProperty(e)||Object.defineProperty(j.prototype,e,{value(...t){return this.toHTMLString()[e](...t)}});function V({element:e,text:t,html:n,range:r,__unstableIsEditableTree:o}={}){return n instanceof j?{text:n.text,formats:n.formats,replacements:n.replacements}:"string"==typeof t&&t.length>0?{formats:Array(t.length),replacements:Array(t.length),text:t}:("string"==typeof n&&n.length>0&&(e=y(document,n)),"object"!=typeof e?{formats:[],replacements:[],text:""}:K({element:e,range:r,isEditableTree:o}))}function I(e,t,n,r){if(!n)return;const{parentNode:o}=t,{startContainer:a,startOffset:s,endContainer:i,endOffset:c}=n,l=e.text.length;void 0!==r.start?e.start=l+r.start:t===a&&t.nodeType===t.TEXT_NODE?e.start=l+s:o===a&&t===a.childNodes[s]?e.start=l:o===a&&t===a.childNodes[s-1]?e.start=l+r.text.length:t===a&&(e.start=l),void 0!==r.end?e.end=l+r.end:t===i&&t.nodeType===t.TEXT_NODE?e.end=l+c:o===i&&t===i.childNodes[c-1]?e.end=l+r.text.length:o===i&&t===i.childNodes[c]?e.end=l:t===i&&(e.end=l+c)}function W(e,t,n){if(!t)return;const{startContainer:r,endContainer:o}=t;let{startOffset:a,endOffset:s}=t;return e===r&&(a=n(e.nodeValue.slice(0,a)).length),e===o&&(s=n(e.nodeValue.slice(0,s)).length),{startContainer:r,startOffset:a,endContainer:o,endOffset:s}}function z(e,t=!0){const n=e.cloneNode(!0);return n.normalize(),Array.from(n.childNodes).forEach(((e,n,r)=>{if(e.nodeType===e.TEXT_NODE){let o=e.nodeValue;/[\n\t\r\f]/.test(o)&&(o=o.replace(/[\n\t\r\f]+/g," ")),-1!==o.indexOf("  ")&&(o=o.replace(/ {2,}/g," ")),0===n&&o.startsWith(" ")?o=o.slice(1):t&&n===r.length-1&&o.endsWith(" ")&&(o=o.slice(0,-1)),e.nodeValue=o}else e.nodeType===e.ELEMENT_NODE&&z(e,!1)})),n}const B="\r";function X(e){return e.replace(new RegExp(`[${E}${v}${B}]`,"gu"),"")}function K({element:e,range:t,isEditableTree:n}){const r={formats:[],replacements:[],text:""};if(!e)return r;if(!e.hasChildNodes())return I(r,e,t,{formats:[],replacements:[],text:""}),r;const o=e.childNodes.length;for(let a=0;a<o;a++){const s=e.childNodes[a],i=s.nodeName.toLowerCase();if(s.nodeType===s.TEXT_NODE){const u=X(s.nodeValue);I(r,s,t=W(s,t,X),{text:u}),r.formats.length+=u.length,r.replacements.length+=u.length,r.text+=u;continue}if(s.nodeType!==s.ELEMENT_NODE)continue;if(n&&"br"===i&&!s.getAttribute("data-rich-text-line-break")){I(r,s,t,{formats:[],replacements:[],text:""});continue}if("script"===i){const d={formats:[,],replacements:[{type:i,attributes:{"data-rich-text-script":s.getAttribute("data-rich-text-script")||encodeURIComponent(s.innerHTML)}}],text:v};I(r,s,t,d),U(r,d);continue}if("br"===i){I(r,s,t,{formats:[],replacements:[],text:""}),U(r,V({text:"\n"}));continue}const c=P({tagName:i,attributes:q({element:s})});if(!1===c?.formatType?.contentEditable){delete c.formatType,I(r,s,t,{formats:[],replacements:[],text:""}),U(r,{formats:[,],replacements:[{...c,innerHTML:s.innerHTML}],text:v});continue}c&&delete c.formatType;const l=K({element:s,range:t,isEditableTree:n});if(I(r,s,t,l),!c||s.getAttribute("data-rich-text-placeholder"))U(r,l);else if(0===l.text.length)c.attributes&&U(r,{formats:[,],replacements:[c],text:v});else{function f(e){if(f.formats===e)return f.newFormats;const t=e?[c,...e]:[c];return f.formats=e,f.newFormats=t,t}f.newFormats=[c],U(r,{...l,formats:Array.from(l.formats,f)})}}return r}function q({element:e}){if(!e.hasAttributes())return;const t=e.attributes.length;let n;for(let r=0;r<t;r++){const{name:t,value:o}=e.attributes[r];if(0===t.indexOf("data-rich-text-"))continue;n=n||{},n[/^on/i.test(t)?"data-disable-rich-text-"+t:t]=o}return n}function U(e,t){return e.formats=e.formats.concat(t.formats),e.replacements=e.replacements.concat(t.replacements),e.text+=t.text,e}function Y(...e){return p(e.reduce(U,V()))}function G(e,t){return T(e).find((({type:e})=>e===t))}function Z({start:e,end:t,replacements:n,text:r}){if(e+1===t&&r[e]===v)return n[e]}function J({start:e,end:t}){if(void 0!==e&&void 0!==t)return e===t}function Q({text:e}){return 0===e.length}function ee(e,t=""){return"string"==typeof t&&(t=V({text:t})),p(e.reduce(((e,{formats:n,replacements:r,text:o})=>({formats:e.formats.concat(t.formats,n),replacements:e.replacements.concat(t.replacements,r),text:e.text+t.text+o}))))}function te(e,t){if("string"==typeof(t={name:e,...t}).name)if(/^[a-z][a-z0-9-]*\/[a-z][a-z0-9-]*$/.test(t.name))if((0,o.select)(f).getFormatType(t.name))window.console.error('Format "'+t.name+'" is already registered.');else if("string"==typeof t.tagName&&""!==t.tagName)if("string"==typeof t.className&&""!==t.className||null===t.className)if(/^[_a-zA-Z]+[a-zA-Z0-9_-]*$/.test(t.className)){if(null===t.className){const e=(0,o.select)(f).getFormatTypeForBareElement(t.tagName);if(e&&"core/unknown"!==e.name)return void window.console.error(`Format "${e.name}" is already registered to handle bare tag name "${t.tagName}".`)}else{const e=(0,o.select)(f).getFormatTypeForClassName(t.className);if(e)return void window.console.error(`Format "${e.name}" is already registered to handle class name "${t.className}".`)}if("title"in t&&""!==t.title)if("keywords"in t&&t.keywords.length>3)window.console.error('The format "'+t.name+'" can have a maximum of 3 keywords.');else{if("string"==typeof t.title)return(0,o.dispatch)(f).addFormatTypes(t),t;window.console.error("Format titles must be strings.")}else window.console.error('The format "'+t.name+'" must have a title.')}else window.console.error("A class name must begin with a letter, followed by any number of hyphens, underscores, letters, or numbers.");else window.console.error("Format class names must be a string, or null to handle bare elements.");else window.console.error("Format tag names must be a string.");else window.console.error("Format names must contain a namespace prefix, include only lowercase alphanumeric characters or dashes, and start with a letter. Example: my-plugin/my-custom-format");else window.console.error("Format names must be strings.")}function ne(e,t,n=e.start,r=e.end){const{formats:o,activeFormats:a}=e,s=o.slice();if(n===r){const e=s[n]?.find((({type:e})=>e===t));if(e){for(;s[n]?.find((t=>t===e));)re(s,n,t),n--;for(r++;s[r]?.find((t=>t===e));)re(s,r,t),r++}}else for(let e=n;e<r;e++)s[e]&&re(s,e,t);return p({...e,formats:s,activeFormats:a?.filter((({type:e})=>e!==t))||[]})}function re(e,t,n){const r=e[t].filter((({type:e})=>e!==n));r.length?e[t]=r:delete e[t]}function oe(e,t,n=e.start,r=e.end){const{formats:o,replacements:a,text:s}=e;"string"==typeof t&&(t=V({text:t}));const i=n+t.text.length;return p({formats:o.slice(0,n).concat(t.formats,o.slice(r)),replacements:a.slice(0,n).concat(t.replacements,a.slice(r)),text:s.slice(0,n)+t.text+s.slice(r),start:i,end:i})}function ae(e,t,n){return oe(e,V(),t,n)}function se({formats:e,replacements:t,text:n,start:r,end:o},a,s){return n=n.replace(a,((n,...a)=>{const i=a[a.length-2];let c,l,u=s;return"function"==typeof u&&(u=s(n,...a)),"object"==typeof u?(c=u.formats,l=u.replacements,u=u.text):(c=Array(u.length),l=Array(u.length),e[i]&&(c=c.fill(e[i]))),e=e.slice(0,i).concat(c,e.slice(i+n.length)),t=t.slice(0,i).concat(l,t.slice(i+n.length)),r&&(r=o=i+u.length),u})),p({formats:e,replacements:t,text:n,start:r,end:o})}function ie(e,t,n,r){return oe(e,{formats:[,],replacements:[t],text:v},n,r)}function ce(e,t=e.start,n=e.end){const{formats:r,replacements:o,text:a}=e;return void 0===t||void 0===n?{...e}:{formats:r.slice(t,n),replacements:o.slice(t,n),text:a.slice(t,n)}}function le({formats:e,replacements:t,text:n,start:r,end:o},a){if("string"!=typeof a)return function({formats:e,replacements:t,text:n,start:r,end:o},a=r,s=o){if(void 0===r||void 0===o)return;const i={formats:e.slice(0,a),replacements:t.slice(0,a),text:n.slice(0,a)},c={formats:e.slice(s),replacements:t.slice(s),text:n.slice(s),start:0,end:0};return[i,c]}(...arguments);let s=0;return n.split(a).map((n=>{const i=s,c={formats:e.slice(i,i+n.length),replacements:t.slice(i,i+n.length),text:n};return s+=a.length+n.length,void 0!==r&&void 0!==o&&(r>=i&&r<s?c.start=r-i:r<i&&o>i&&(c.start=0),o>=i&&o<s?c.end=o-i:r<s&&o>s&&(c.end=n.length)),c}))}function ue(e,t){return e===t||e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset}function de(e,t,n){const r=e.parentNode;let o=0;for(;e=e.previousSibling;)o++;return n=[o,...n],r!==t&&(n=de(r,t,n)),n}function fe(e,t){for(t=[...t];e&&t.length>1;)e=e.childNodes[t.shift()];return{node:e,offset:t[0]}}function me(e,t){if(void 0!==t.html)return e.innerHTML+=t.html;"string"==typeof t&&(t=e.ownerDocument.createTextNode(t));const{type:n,attributes:r}=t;if(n){t=e.ownerDocument.createElement(n);for(const e in r)t.setAttribute(e,r[e])}return e.appendChild(t)}function pe(e,t){e.appendData(t)}function ge({lastChild:e}){return e}function he({parentNode:e}){return e}function ye(e){return e.nodeType===e.TEXT_NODE}function ve({nodeValue:e}){return e}function Ee(e){return e.parentNode.removeChild(e)}function be({value:e,prepareEditableTree:t,isEditableTree:n=!0,placeholder:r,doc:o=document}){let a=[],s=[];t&&(e={...e,formats:t(e)});return{body:C({value:e,createEmpty:()=>y(o,""),append:me,getLastChild:ge,getParent:he,isText:ye,getText:ve,remove:Ee,appendText:pe,onStartIndex(e,t){a=de(t,e,[t.nodeValue.length])},onEndIndex(e,t){s=de(t,e,[t.nodeValue.length])},isEditableTree:n,placeholder:r}),selection:{startPath:a,endPath:s}}}function Te({value:e,current:t,prepareEditableTree:n,__unstableDomOnly:r,placeholder:o}){const{body:a,selection:s}=be({value:e,prepareEditableTree:n,placeholder:o,doc:t.ownerDocument});xe(a,t),void 0===e.start||r||function({startPath:e,endPath:t},n){const{node:r,offset:o}=fe(n,e),{node:a,offset:s}=fe(n,t),{ownerDocument:i}=n,{defaultView:c}=i,l=c.getSelection(),u=i.createRange();u.setStart(r,o),u.setEnd(a,s);const{activeElement:d}=i;if(l.rangeCount>0){if(ue(u,l.getRangeAt(0)))return;l.removeAllRanges()}l.addRange(u),d!==i.activeElement&&d instanceof c.HTMLElement&&d.focus()}(s,t)}function xe(e,t){let n,r=0;for(;n=e.firstChild;){const o=t.childNodes[r];if(o)if(o.isEqualNode(n))e.removeChild(n);else if(o.nodeName!==n.nodeName||o.nodeType===o.TEXT_NODE&&o.data!==n.data)t.replaceChild(n,o);else{const t=o.attributes,r=n.attributes;if(t){let e=t.length;for(;e--;){const{name:r}=t[e];n.getAttribute(r)||o.removeAttribute(r)}}if(r)for(let e=0;e<r.length;e++){const{name:t,value:n}=r[e];o.getAttribute(t)!==n&&o.setAttribute(t,n)}xe(n,o),e.removeChild(n)}else t.appendChild(n);r++}for(;t.childNodes[r];)t.removeChild(t.childNodes[r])}const we=window.wp.a11y,Ne=window.wp.i18n;function Le(e,t){return G(e,t.type)?(t.title&&(0,we.speak)((0,Ne.sprintf)((0,Ne.__)("%s removed."),t.title),"assertive"),ne(e,t.type)):(t.title&&(0,we.speak)((0,Ne.sprintf)((0,Ne.__)("%s applied."),t.title),"assertive"),h(e,t))}function Ce(e){const t=(0,o.select)(f).getFormatType(e);if(t)return(0,o.dispatch)(f).removeFormatTypes(e),t;window.console.error(`Format ${e} is not registered.`)}const _e=window.wp.element,Fe=window.wp.deprecated;var Oe=e.n(Fe);function Se({ref:e,value:t,settings:n={}}){Oe()("`useAnchorRef` hook",{since:"6.1",alternative:"`useAnchor` hook"});const{tagName:r,className:o,name:a}=n,s=a?G(t,a):void 0;return(0,_e.useMemo)((()=>{if(!e.current)return;const{ownerDocument:{defaultView:t}}=e.current,n=t.getSelection();if(!n.rangeCount)return;const a=n.getRangeAt(0);if(!s)return a;let i=a.startContainer;for(i=i.nextElementSibling||i;i.nodeType!==i.ELEMENT_NODE;)i=i.parentNode;return i.closest(r+(o?"."+o:""))}),[s,t.start,t.end,r,o])}const Ae=window.wp.compose;function Re(e,t,n){if(!e)return;const{ownerDocument:r}=e,{defaultView:o}=r,a=o.getSelection();if(!a)return;if(!a.rangeCount)return;const s=a.getRangeAt(0);if(!s||!s.startContainer)return;const i=function(e,t,n,r){let o=e.startContainer;if(o.nodeType===o.TEXT_NODE&&e.startOffset===o.length&&o.nextSibling)for(o=o.nextSibling;o.firstChild;)o=o.firstChild;if(o.nodeType!==o.ELEMENT_NODE&&(o=o.parentElement),!o)return;if(o===t)return;if(!t.contains(o))return;const a=n+(r?"."+r:"");for(;o!==t;){if(o.matches(a))return o;o=o.parentElement}}(s,e,t,n);return i||function(e,t){return{contextElement:t,getBoundingClientRect:()=>t.contains(e.startContainer)?e.getBoundingClientRect():t.getBoundingClientRect()}}(s,e)}function De({editableContentElement:e,settings:t={}}){const{tagName:n,className:r,isActive:o}=t,[a,s]=(0,_e.useState)((()=>Re(e,n,r))),i=(0,Ae.usePrevious)(o);return(0,_e.useLayoutEffect)((()=>{if(!e)return;function t(){s(Re(e,n,r))}function a(){l.addEventListener("selectionchange",t)}function c(){l.removeEventListener("selectionchange",t)}const{ownerDocument:l}=e;return(e===l.activeElement||!i&&o||i&&!o)&&(s(Re(e,n,r)),a()),e.addEventListener("focusin",a),e.addEventListener("focusout",c),()=>{c(),e.removeEventListener("focusin",a),e.removeEventListener("focusout",c)}}),[e,n,r,o,i]),a}const Me="pre-wrap",ke="1px";function $e({record:e}){const t=(0,_e.useRef)(),{activeFormats:n=[],replacements:r,start:o}=e.current,a=r[o];return(0,_e.useEffect)((()=>{if(!(n&&n.length||a))return;const e="*[data-rich-text-format-boundary]",r=t.current.querySelector(e);if(!r)return;const{ownerDocument:o}=r,{defaultView:s}=o,i=`${`.rich-text:focus ${e}`} {${`background-color: ${s.getComputedStyle(r).color.replace(")",", 0.2)").replace("rgb","rgba")}`}}`,c="rich-text-boundary-style";let l=o.getElementById(c);l||(l=o.createElement("style"),l.id=c,o.head.appendChild(l)),l.innerHTML!==i&&(l.innerHTML=i)}),[n,a]),t}const He=window.wp.keycodes,Pe=[];const je=new Set(["insertParagraph","insertOrderedList","insertUnorderedList","insertHorizontalRule","insertLink"]),Ve=[],Ie="data-rich-text-placeholder";const We=[e=>t=>{function n(n){const{record:r}=e.current,{ownerDocument:o}=t;if(J(r.current)||!t.contains(o.activeElement))return;const a=ce(r.current),s=H(a),i=_({value:a});n.clipboardData.setData("text/plain",s),n.clipboardData.setData("text/html",i),n.clipboardData.setData("rich-text","true"),n.preventDefault(),"cut"===n.type&&o.execCommand("delete")}const{defaultView:r}=t.ownerDocument;return r.addEventListener("copy",n),r.addEventListener("cut",n),()=>{r.removeEventListener("copy",n),r.removeEventListener("cut",n)}},()=>e=>{function t(t){const{target:n}=t;if(n===e||n.textContent&&n.isContentEditable)return;const{ownerDocument:r}=n,{defaultView:o}=r,a=o.getSelection();if(a.containsNode(n))return;const s=r.createRange(),i=n.isContentEditable?n:n.closest("[contenteditable]");s.selectNode(i),a.removeAllRanges(),a.addRange(s),t.preventDefault()}function n(n){n.relatedTarget&&!e.contains(n.relatedTarget)&&"A"===n.relatedTarget.tagName&&t(n)}return e.addEventListener("click",t),e.addEventListener("focusin",n),()=>{e.removeEventListener("click",t),e.removeEventListener("focusin",n)}},e=>t=>{function n(n){const{keyCode:r,shiftKey:o,altKey:a,metaKey:s,ctrlKey:i}=n;if(o||a||s||i||r!==He.LEFT&&r!==He.RIGHT)return;const{record:c,applyRecord:l,forceRender:u}=e.current,{text:d,formats:f,start:m,end:p,activeFormats:g=[]}=c.current,h=J(c.current),{ownerDocument:y}=t,{defaultView:v}=y,{direction:E}=v.getComputedStyle(t),b="rtl"===E?He.RIGHT:He.LEFT,T=n.keyCode===b;if(h&&0===g.length){if(0===m&&T)return;if(p===d.length&&!T)return}if(!h)return;const x=f[m-1]||Pe,w=f[m]||Pe,N=T?x:w,L=g.every(((e,t)=>e===N[t]));let C=g.length;if(L?C<N.length&&C++:C--,C===g.length)return void(c.current._newActiveFormats=N);n.preventDefault();const _=(L?N:T?w:x).slice(0,C),F={...c.current,activeFormats:_};c.current=F,l(F),u()}return t.addEventListener("keydown",n),()=>{t.removeEventListener("keydown",n)}},e=>t=>{function n(t){const{keyCode:n}=t,{createRecord:r,handleChange:o}=e.current;if(t.defaultPrevented)return;if(n!==He.DELETE&&n!==He.BACKSPACE)return;const a=r(),{start:s,end:i,text:c}=a;0===s&&0!==i&&i===c.length&&(o(ae(a)),t.preventDefault())}return t.addEventListener("keydown",n),()=>{t.removeEventListener("keydown",n)}},e=>t=>{const{ownerDocument:n}=t,{defaultView:r}=n;let o=!1;function a(t){if(o)return;let n;t&&(n=t.inputType);const{record:r,applyRecord:a,createRecord:s,handleChange:i}=e.current;if(n&&(0===n.indexOf("format")||je.has(n)))return void a(r.current);const c=s(),{start:l,activeFormats:u=[]}=r.current,d=function({value:e,start:t,end:n,formats:r}){const o=Math.min(t,n),a=Math.max(t,n),s=e.formats[o-1]||[],i=e.formats[a]||[];for(e.activeFormats=r.map(((e,t)=>{if(s[t]){if(m(e,s[t]))return s[t]}else if(i[t]&&m(e,i[t]))return i[t];return e}));--n>=t;)e.activeFormats.length>0?e.formats[n]=e.activeFormats:delete e.formats[n];return e}({value:c,start:l,end:c.start,formats:u});i(d)}function s(){const{record:i,applyRecord:c,createRecord:l,onSelectionChange:u}=e.current;if("true"!==t.contentEditable)return;if(n.activeElement!==t)return void n.removeEventListener("selectionchange",s);if(o)return;const{start:d,end:f,text:m}=l(),p=i.current;if(m!==p.text)return void a();if(d===p.start&&f===p.end)return void(0===p.text.length&&0===d&&function(e){const t=e.getSelection(),{anchorNode:n,anchorOffset:r}=t;if(n.nodeType!==n.ELEMENT_NODE)return;const o=n.childNodes[r];o&&o.nodeType===o.ELEMENT_NODE&&o.hasAttribute(Ie)&&t.collapseToStart()}(r));const g={...p,start:d,end:f,activeFormats:p._newActiveFormats,_newActiveFormats:void 0},h=T(g,Ve);g.activeFormats=h,i.current=g,c(g,{domOnly:!0}),u(d,f)}function i(){o=!0,n.removeEventListener("selectionchange",s),t.querySelector(`[${Ie}]`)?.remove()}function c(){o=!1,a({inputType:"insertText"}),n.addEventListener("selectionchange",s)}function l(){const{record:r,isSelected:o,onSelectionChange:a,applyRecord:i}=e.current;if(!t.parentElement.closest('[contenteditable="true"]')){if(o)i(r.current,{domOnly:!0});else{const e=void 0;r.current={...r.current,start:e,end:e,activeFormats:Ve}}a(r.current.start,r.current.end),window.queueMicrotask(s),n.addEventListener("selectionchange",s)}}return t.addEventListener("input",a),t.addEventListener("compositionstart",i),t.addEventListener("compositionend",c),t.addEventListener("focus",l),()=>{t.removeEventListener("input",a),t.removeEventListener("compositionstart",i),t.removeEventListener("compositionend",c),t.removeEventListener("focus",l)}},()=>e=>{const{ownerDocument:t}=e,{defaultView:n}=t,r=n?.getSelection();let o;function a(){return r.rangeCount?r.getRangeAt(0):null}function s(e){const n="keydown"===e.type?"keyup":"pointerup";function r(){t.removeEventListener(n,s),t.removeEventListener("selectionchange",r),t.removeEventListener("input",r)}function s(){r(),ue(o,a())||t.dispatchEvent(new Event("selectionchange"))}t.addEventListener(n,s),t.addEventListener("selectionchange",r),t.addEventListener("input",r),o=a()}return e.addEventListener("pointerdown",s),e.addEventListener("keydown",s),()=>{e.removeEventListener("pointerdown",s),e.removeEventListener("keydown",s)}}];function ze(e){const t=(0,_e.useRef)(e);t.current=e;const n=(0,_e.useMemo)((()=>We.map((e=>e(t)))),[t]);return(0,Ae.useRefEffect)((e=>{const t=n.map((t=>t(e)));return()=>{t.forEach((e=>e()))}}),[n])}function Be({value:e="",selectionStart:t,selectionEnd:n,placeholder:r,onSelectionChange:a,preserveWhiteSpace:s,onChange:i,__unstableDisableFormats:c,__unstableIsSelected:l,__unstableDependencies:u=[],__unstableAfterParse:d,__unstableBeforeSerialize:f,__unstableAddInvisibleFormats:m}){const p=(0,o.useRegistry)(),[,g]=(0,_e.useReducer)((()=>({}))),h=(0,_e.useRef)();function y(e,{domOnly:t}={}){Te({value:e,current:h.current,prepareEditableTree:m,__unstableDomOnly:t,placeholder:r})}const v=(0,_e.useRef)(e),E=(0,_e.useRef)();function b(){v.current=e,E.current=e,e instanceof j||(E.current=e?j.fromHTMLString(e,{preserveWhiteSpace:s}):j.empty()),E.current={text:E.current.text,formats:E.current.formats,replacements:E.current.replacements},c&&(E.current.formats=Array(e.length),E.current.replacements=Array(e.length)),d&&(E.current.formats=d(E.current)),E.current.start=t,E.current.end=n}const T=(0,_e.useRef)(!1);function x(t){if(E.current=t,y(t),c)v.current=t.text;else{const n=f?f(t):t.formats;t={...t,formats:n},v.current="string"==typeof e?_({value:t,preserveWhiteSpace:s}):new j(t)}const{start:n,end:r,formats:o,text:l}=E.current;p.batch((()=>{a(n,r),i(v.current,{__unstableFormats:o,__unstableText:l})})),g()}function w(){b(),y(E.current)}E.current?t===E.current.start&&n===E.current.end||(T.current=l,E.current={...E.current,start:t,end:n,activeFormats:void 0}):(T.current=l,b());const N=(0,_e.useRef)(!1);(0,_e.useLayoutEffect)((()=>{N.current&&e!==v.current&&(w(),g())}),[e]),(0,_e.useLayoutEffect)((()=>{T.current&&(h.current.ownerDocument.activeElement!==h.current&&h.current.focus(),y(E.current),T.current=!1)}),[T.current]);const L=(0,Ae.useMergeRefs)([h,(0,_e.useCallback)((e=>{e&&(e.style.whiteSpace=Me,e.style.minWidth=ke)}),[]),$e({record:E}),ze({record:E,handleChange:x,applyRecord:y,createRecord:function(){const{ownerDocument:{defaultView:e}}=h.current,t=e.getSelection(),n=t.rangeCount>0?t.getRangeAt(0):null;return V({element:h.current,range:n,__unstableIsEditableTree:!0})},isSelected:l,onSelectionChange:a,forceRender:g}),(0,Ae.useRefEffect)((()=>{w(),N.current=!0}),[r,...u])]);return{value:E.current,getValue:()=>E.current,onChange:x,ref:L}}function Xe(){}(window.wp=window.wp||{}).richText=t})();