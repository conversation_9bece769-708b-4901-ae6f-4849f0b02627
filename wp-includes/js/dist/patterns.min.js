/*! This file is auto-generated */
(()=>{"use strict";var e={d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{privateApis:()=>ee,store:()=>k});var n={};e.r(n),e.d(n,{convertSyncedPatternToStatic:()=>h,createPattern:()=>m,createPatternFromFile:()=>g,setEditingPattern:()=>y});var r={};e.r(r),e.d(r,{isEditingPattern:()=>f});const a=window.wp.data;const s=(0,a.combineReducers)({isEditingPattern:function(e={},t){return"SET_EDITING_PATTERN"===t?.type?{...e,[t.clientId]:t.isEditing}:e}}),o=window.wp.blocks,i=window.wp.coreData,c=window.wp.blockEditor,l={theme:"pattern",user:"wp_block"},d="all-patterns",u={full:"fully",unsynced:"unsynced"},p={"core/paragraph":["content"],"core/heading":["content"],"core/button":["text","url","linkTarget","rel"],"core/image":["id","url","title","alt"]},_="core/pattern-overrides",m=(e,t,n,r)=>async({registry:a})=>{const s=t===u.unsynced?{wp_pattern_sync_status:t}:void 0,o={title:e,content:n,status:"publish",meta:s,wp_pattern_category:r};return await a.dispatch(i.store).saveEntityRecord("postType","wp_block",o)},g=(e,t)=>async({dispatch:n})=>{const r=await e.text();let a;try{a=JSON.parse(r)}catch(e){throw new Error("Invalid JSON file")}if("wp_block"!==a.__file||!a.title||!a.content||"string"!=typeof a.title||"string"!=typeof a.content||a.syncStatus&&"string"!=typeof a.syncStatus)throw new Error("Invalid pattern JSON file");return await n.createPattern(a.title,a.syncStatus,a.content,t)},h=e=>({registry:t})=>{const n=t.select(c.store).getBlock(e),r=n.attributes?.content;const a=t.select(c.store).getBlocks(n.clientId);t.dispatch(c.store).replaceBlocks(n.clientId,function e(t){return t.map((t=>{let n=t.attributes.metadata;if(n&&(n={...n},delete n.id,delete n.bindings,r?.[n.name]))for(const[e,a]of Object.entries(r[n.name]))(0,o.getBlockType)(t.name)?.attributes[e]&&(t.attributes[e]=a);return(0,o.cloneBlock)(t,{metadata:n&&Object.keys(n).length>0?n:void 0},e(t.innerBlocks))}))}(a))};function y(e,t){return{type:"SET_EDITING_PATTERN",clientId:e,isEditing:t}}function f(e,t){return e.isEditingPattern[t]}const x=window.wp.privateApis,{lock:b,unlock:v}=(0,x.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/patterns"),w={reducer:s},k=(0,a.createReduxStore)("core/patterns",{...w});(0,a.register)(k),v(k).registerPrivateActions(n),v(k).registerPrivateSelectors(r);const S=window.wp.components,C=window.wp.element,j=window.wp.i18n;function B(e){return Object.keys(p).includes(e.name)&&!!e.attributes.metadata?.name&&!!e.attributes.metadata?.bindings&&Object.values(e.attributes.metadata.bindings).some((e=>"core/pattern-overrides"===e.source))}const P=window.ReactJSXRuntime,{BlockQuickNavigation:T}=v(c.privateApis);const E=window.wp.notices,D=window.wp.compose,I=window.wp.htmlEntities,N=e=>(0,I.decodeEntities)(e),R="wp_pattern_category";function M({categoryTerms:e,onChange:t,categoryMap:n}){const[r,a]=(0,C.useState)(""),s=(0,D.useDebounce)(a,500),o=(0,C.useMemo)((()=>Array.from(n.values()).map((e=>N(e.label))).filter((e=>""===r||e.toLowerCase().includes(r.toLowerCase()))).sort(((e,t)=>e.localeCompare(t)))),[r,n]);return(0,P.jsx)(S.FormTokenField,{className:"patterns-menu-items__convert-modal-categories",value:e,suggestions:o,onChange:function(e){const n=e.reduce(((e,t)=>(e.some((e=>e.toLowerCase()===t.toLowerCase()))||e.push(t),e)),[]);t(n)},onInputChange:s,label:(0,j.__)("Categories"),tokenizeOnBlur:!0,__experimentalExpandOnFocus:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0})}function O(){const{saveEntityRecord:e,invalidateResolution:t}=(0,a.useDispatch)(i.store),{corePatternCategories:n,userPatternCategories:r}=(0,a.useSelect)((e=>{const{getUserPatternCategories:t,getBlockPatternCategories:n}=e(i.store);return{corePatternCategories:n(),userPatternCategories:t()}}),[]),s=(0,C.useMemo)((()=>{const e=new Map;return r.forEach((t=>{e.set(t.label.toLowerCase(),{label:t.label,name:t.name,id:t.id})})),n.forEach((t=>{e.has(t.label.toLowerCase())||"query"===t.name||e.set(t.label.toLowerCase(),{label:t.label,name:t.name})})),e}),[r,n]);return{categoryMap:s,findOrCreateTerm:async function(n){try{const r=s.get(n.toLowerCase());if(r?.id)return r.id;const a=r?{name:r.label,slug:r.name}:{name:n},o=await e("taxonomy",R,a,{throwOnError:!0});return t("getUserPatternCategories"),o.id}catch(e){if("term_exists"!==e.code)throw e;return e.data.term_id}}}}function A({className:e="patterns-menu-items__convert-modal",modalTitle:t,...n}){const r=(0,a.useSelect)((e=>e(i.store).getPostType(l.user)?.labels?.add_new_item),[]);return(0,P.jsx)(S.Modal,{title:t||r,onRequestClose:n.onClose,overlayClassName:e,focusOnMount:"firstContentElement",size:"small",children:(0,P.jsx)(z,{...n})})}function z({confirmLabel:e=(0,j.__)("Add"),defaultCategories:t=[],content:n,onClose:r,onError:s,onSuccess:o,defaultSyncType:i=u.full,defaultTitle:c=""}){const[l,p]=(0,C.useState)(i),[_,m]=(0,C.useState)(t),[g,h]=(0,C.useState)(c),[y,f]=(0,C.useState)(!1),{createPattern:x}=v((0,a.useDispatch)(k)),{createErrorNotice:b}=(0,a.useDispatch)(E.store),{categoryMap:w,findOrCreateTerm:B}=O();return(0,P.jsx)("form",{onSubmit:e=>{e.preventDefault(),async function(e,t){if(g&&!y)try{f(!0);const r=await Promise.all(_.map((e=>B(e)))),a=await x(e,t,"function"==typeof n?n():n,r);o({pattern:a,categoryId:d})}catch(e){b(e.message,{type:"snackbar",id:"pattern-create"}),s?.()}finally{f(!1),m([]),h("")}}(g,l)},children:(0,P.jsxs)(S.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(S.TextControl,{label:(0,j.__)("Name"),value:g,onChange:h,placeholder:(0,j.__)("My pattern"),className:"patterns-create-modal__name-input",__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,P.jsx)(M,{categoryTerms:_,onChange:m,categoryMap:w}),(0,P.jsx)(S.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,j._x)("Synced","pattern (singular)"),help:(0,j.__)("Sync this pattern across multiple locations."),checked:l===u.full,onChange:()=>{p(l===u.full?u.unsynced:u.full)}}),(0,P.jsxs)(S.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{r(),h("")},children:(0,j.__)("Cancel")}),(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!g||y,isBusy:y,children:e})]})]})})}function L(e,t){return e.type!==l.user?t.core?.filter((t=>e.categories?.includes(t.name))).map((e=>e.label)):t.user?.filter((t=>e.wp_pattern_category.includes(t.id))).map((e=>e.label))}function U({pattern:e,onSuccess:t}){const{createSuccessNotice:n}=(0,a.useDispatch)(E.store),r=(0,a.useSelect)((e=>{const{getUserPatternCategories:t,getBlockPatternCategories:n}=e(i.store);return{core:n(),user:t()}}));return e?{content:e.content,defaultCategories:L(e,r),defaultSyncType:e.type!==l.user?u.unsynced:e.wp_pattern_sync_status||u.full,defaultTitle:(0,j.sprintf)((0,j._x)("%s (Copy)","pattern"),"string"==typeof e.title?e.title:e.title.raw),onSuccess:({pattern:e})=>{n((0,j.sprintf)((0,j._x)('"%s" duplicated.',"pattern"),e.title.raw),{type:"snackbar",id:"patterns-create"}),t?.({pattern:e})}}:null}const H=window.wp.primitives,V=(0,P.jsx)(H.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(H.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.1.1.1.3 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})});function F({clientIds:e,rootClientId:t,closeBlockSettingsMenu:n}){const{createSuccessNotice:r}=(0,a.useDispatch)(E.store),{replaceBlocks:s}=(0,a.useDispatch)(c.store),{setEditingPattern:l}=v((0,a.useDispatch)(k)),[d,p]=(0,C.useState)(!1),_=(0,a.useSelect)((n=>{var r;const{canUser:a}=n(i.store),{getBlocksByClientId:s,canInsertBlockType:l,getBlockRootClientId:d}=n(c.store),u=t||(e.length>0?d(e[0]):void 0),p=null!==(r=s(e))&&void 0!==r?r:[];return!(1===p.length&&p[0]&&(0,o.isReusableBlock)(p[0])&&!!n(i.store).getEntityRecord("postType","wp_block",p[0].attributes.ref))&&l("core/block",u)&&p.every((e=>!!e&&e.isValid&&(0,o.hasBlockSupport)(e.name,"reusable",!0)))&&!!a("create",{kind:"postType",name:"wp_block"})}),[e,t]),{getBlocksByClientId:m}=(0,a.useSelect)(c.store),g=(0,C.useCallback)((()=>(0,o.serialize)(m(e))),[m,e]);if(!_)return null;return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(S.MenuItem,{icon:V,onClick:()=>p(!0),"aria-expanded":d,"aria-haspopup":"dialog",children:(0,j.__)("Create pattern")}),d&&(0,P.jsx)(A,{content:g,onSuccess:t=>{(({pattern:t})=>{if(t.wp_pattern_sync_status!==u.unsynced){const r=(0,o.createBlock)("core/block",{ref:t.id});s(e,r),l(r.clientId,!0),n()}r(t.wp_pattern_sync_status===u.unsynced?(0,j.sprintf)((0,j.__)("Unsynced pattern created: %s"),t.title.raw):(0,j.sprintf)((0,j.__)("Synced pattern created: %s"),t.title.raw),{type:"snackbar",id:"convert-to-pattern-success"}),p(!1)})(t)},onError:()=>{p(!1)},onClose:()=>{p(!1)}})]})}const q=window.wp.url;const G=function({clientId:e}){const{canRemove:t,isVisible:n,managePatternsUrl:r}=(0,a.useSelect)((t=>{const{getBlock:n,canRemoveBlock:r,getBlockCount:a}=t(c.store),{canUser:s}=t(i.store),l=n(e);return{canRemove:r(e),isVisible:!!l&&(0,o.isReusableBlock)(l)&&!!s("update",{kind:"postType",name:"wp_block",id:l.attributes.ref}),innerBlockCount:a(e),managePatternsUrl:s("create",{kind:"postType",name:"wp_template"})?(0,q.addQueryArgs)("site-editor.php",{path:"/patterns"}):(0,q.addQueryArgs)("edit.php",{post_type:"wp_block"})}}),[e]),{convertSyncedPatternToStatic:s}=v((0,a.useDispatch)(k));return n?(0,P.jsxs)(P.Fragment,{children:[t&&(0,P.jsx)(S.MenuItem,{onClick:()=>s(e),children:(0,j.__)("Detach")}),(0,P.jsx)(S.MenuItem,{href:r,children:(0,j.__)("Manage patterns")})]}):null};const Y=window.wp.a11y;function J({placeholder:e,initialName:t="",onClose:n,onSave:r}){const[a,s]=(0,C.useState)(t),o=(0,C.useId)(),i=!!a.trim();return(0,P.jsx)(S.Modal,{title:(0,j.__)("Enable overrides"),onRequestClose:n,focusOnMount:"firstContentElement",aria:{describedby:o},size:"small",children:(0,P.jsx)("form",{onSubmit:e=>{e.preventDefault(),i&&(()=>{if(a!==t){const e=(0,j.sprintf)((0,j.__)('Block name changed to: "%s".'),a);(0,Y.speak)(e,"assertive")}r(a),n()})()},children:(0,P.jsxs)(S.__experimentalVStack,{spacing:"6",children:[(0,P.jsx)(S.__experimentalText,{id:o,children:(0,j.__)("Overrides are changes you make to a block within a synced pattern instance. Use overrides to customize a synced pattern instance to suit its new context. Name this block to specify an override.")}),(0,P.jsx)(S.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,value:a,label:(0,j.__)("Name"),help:(0,j.__)('For example, if you are creating a recipe pattern, you use "Recipe Title", "Recipe Description", etc.'),placeholder:e,onChange:s}),(0,P.jsxs)(S.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:n,children:(0,j.__)("Cancel")}),(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,"aria-disabled":!i,variant:"primary",type:"submit",children:(0,j.__)("Enable")})]})]})})})}function Q({onClose:e,onSave:t}){const n=(0,C.useId)();return(0,P.jsx)(S.Modal,{title:(0,j.__)("Disable overrides"),onRequestClose:e,aria:{describedby:n},size:"small",children:(0,P.jsx)("form",{onSubmit:n=>{n.preventDefault(),t(),e()},children:(0,P.jsxs)(S.__experimentalVStack,{spacing:"6",children:[(0,P.jsx)(S.__experimentalText,{id:n,children:(0,j.__)("Are you sure you want to disable overrides? Disabling overrides will revert all applied overrides for this block throughout instances of this pattern.")}),(0,P.jsxs)(S.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:e,children:(0,j.__)("Cancel")}),(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",children:(0,j.__)("Disable")})]})]})})})}const W=function({attributes:e,setAttributes:t,name:n}){const r=(0,C.useId)(),[a,s]=(0,C.useState)(!1),[o,i]=(0,C.useState)(!1),l=!!e.metadata?.name,d=e.metadata?.bindings?.__default,u=l&&d?.source===_,p=d?.source&&d.source!==_,{updateBlockBindings:m}=(0,c.useBlockBindingsUtils)();function g(n,r){r&&t({metadata:{...e.metadata,name:r}}),m({__default:n?{source:_}:void 0})}if(p)return null;const h=!("core/image"!==n||!e.caption?.length&&!e.href?.length),y=!u&&h?(0,j.__)("Overrides currently don't support image captions or links. Remove the caption or link first before enabling overrides."):(0,j.__)("Allow changes to this block throughout instances of this pattern.");return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(c.InspectorControls,{group:"advanced",children:(0,P.jsx)(S.BaseControl,{__nextHasNoMarginBottom:!0,id:r,label:(0,j.__)("Overrides"),help:y,children:(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,className:"pattern-overrides-control__allow-overrides-button",variant:"secondary","aria-haspopup":"dialog",onClick:()=>{u?i(!0):s(!0)},disabled:!u&&h,accessibleWhenDisabled:!0,children:u?(0,j.__)("Disable overrides"):(0,j.__)("Enable overrides")})})}),a&&(0,P.jsx)(J,{initialName:e.metadata?.name,onClose:()=>s(!1),onSave:e=>{g(!0,e)}}),o&&(0,P.jsx)(Q,{onClose:()=>i(!1),onSave:()=>g(!1)})]})},Z="content";const $=(0,P.jsx)(H.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,P.jsx)(H.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5 4.5h11a.5.5 0 0 1 .5.5v11a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V5a.5.5 0 0 1 .5-.5ZM3 5a2 2 0 0 1 2-2h11a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5Zm17 3v10.75c0 .69-.56 1.25-1.25 1.25H6v1.5h12.75a2.75 2.75 0 0 0 2.75-2.75V8H20Z"})}),{useBlockDisplayTitle:X}=v(c.privateApis);function K({clientIds:e}){const t=1===e.length,{icon:n,firstBlockName:r}=(0,a.useSelect)((n=>{const{getBlockAttributes:r,getBlockNamesByClientId:a}=n(c.store),{getBlockType:s,getActiveBlockVariation:i}=n(o.store),l=a(e),d=l[0],u=s(d);let p;if(t){const t=i(d,r(e[0]));p=t?.icon||u.icon}else{p=1===new Set(l).size?u.icon:$}return{icon:p,firstBlockName:r(e[0]).metadata.name}}),[e,t]),s=X({clientId:e[0],maximumLength:35}),i=t?(0,j.sprintf)((0,j.__)('This %1$s is editable using the "%2$s" override.'),s.toLowerCase(),r):(0,j.__)("These blocks are editable using overrides."),l=(0,C.useId)();return(0,P.jsx)(S.ToolbarItem,{children:e=>(0,P.jsx)(S.DropdownMenu,{className:"patterns-pattern-overrides-toolbar-indicator",label:s,popoverProps:{placement:"bottom-start",className:"patterns-pattern-overrides-toolbar-indicator__popover"},icon:(0,P.jsx)(P.Fragment,{children:(0,P.jsx)(c.BlockIcon,{icon:n,className:"patterns-pattern-overrides-toolbar-indicator-icon",showColors:!0})}),toggleProps:{description:i,...e},menuProps:{orientation:"both","aria-describedby":l},children:()=>(0,P.jsx)(S.__experimentalText,{id:l,children:i})})})}const ee={};b(ee,{OverridesPanel:function(){const e=(0,a.useSelect)((e=>e(c.store).getClientIdsWithDescendants()),[]),{getBlock:t}=(0,a.useSelect)(c.store),n=(0,C.useMemo)((()=>e.filter((e=>B(t(e))))),[e,t]);return n?.length?(0,P.jsx)(S.PanelBody,{title:(0,j.__)("Overrides"),children:(0,P.jsx)(T,{clientIds:n})}):null},CreatePatternModal:A,CreatePatternModalContents:z,DuplicatePatternModal:function({pattern:e,onClose:t,onSuccess:n}){const r=U({pattern:e,onSuccess:n});return e?(0,P.jsx)(A,{modalTitle:(0,j.__)("Duplicate pattern"),confirmLabel:(0,j.__)("Duplicate"),onClose:t,onError:t,...r}):null},isOverridableBlock:B,hasOverridableBlocks:function e(t){return t.some((t=>!!B(t)||e(t.innerBlocks)))},useDuplicatePatternProps:U,RenamePatternModal:function({onClose:e,onError:t,onSuccess:n,pattern:r,...s}){const o=(0,I.decodeEntities)(r.title),[c,l]=(0,C.useState)(o),[d,u]=(0,C.useState)(!1),{editEntityRecord:p,__experimentalSaveSpecifiedEntityEdits:_}=(0,a.useDispatch)(i.store),{createSuccessNotice:m,createErrorNotice:g}=(0,a.useDispatch)(E.store);return(0,P.jsx)(S.Modal,{title:(0,j.__)("Rename"),...s,onRequestClose:e,focusOnMount:"firstContentElement",size:"small",children:(0,P.jsx)("form",{onSubmit:async a=>{if(a.preventDefault(),c&&c!==r.title&&!d)try{await p("postType",r.type,r.id,{title:c}),u(!0),l(""),e?.();const t=await _("postType",r.type,r.id,["title"],{throwOnError:!0});n?.(t),m((0,j.__)("Pattern renamed"),{type:"snackbar",id:"pattern-update"})}catch(e){t?.();const n=e.message&&"unknown_error"!==e.code?e.message:(0,j.__)("An error occurred while renaming the pattern.");g(n,{type:"snackbar",id:"pattern-update"})}finally{u(!1),l("")}},children:(0,P.jsxs)(S.__experimentalVStack,{spacing:"5",children:[(0,P.jsx)(S.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,j.__)("Name"),value:c,onChange:l,required:!0}),(0,P.jsxs)(S.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{e?.(),l("")},children:(0,j.__)("Cancel")}),(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",children:(0,j.__)("Save")})]})]})})})},PatternsMenuItems:function({rootClientId:e}){return(0,P.jsx)(c.BlockSettingsMenuControls,{children:({selectedClientIds:t,onClose:n})=>(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(F,{clientIds:t,rootClientId:e,closeBlockSettingsMenu:n}),1===t.length&&(0,P.jsx)(G,{clientId:t[0]})]})})},RenamePatternCategoryModal:function({category:e,existingCategories:t,onClose:n,onError:r,onSuccess:s,...o}){const c=(0,C.useId)(),l=(0,C.useRef)(),[d,u]=(0,C.useState)((0,I.decodeEntities)(e.name)),[p,_]=(0,C.useState)(!1),[m,g]=(0,C.useState)(!1),h=m?`patterns-rename-pattern-category-modal__validation-message-${c}`:void 0,{saveEntityRecord:y,invalidateResolution:f}=(0,a.useDispatch)(i.store),{createErrorNotice:x,createSuccessNotice:b}=(0,a.useDispatch)(E.store),v=()=>{n(),u("")};return(0,P.jsx)(S.Modal,{title:(0,j.__)("Rename"),onRequestClose:v,...o,children:(0,P.jsx)("form",{onSubmit:async a=>{if(a.preventDefault(),!p){if(!d||d===e.name){const e=(0,j.__)("Please enter a new name for this category.");return(0,Y.speak)(e,"assertive"),g(e),void l.current?.focus()}if(t.patternCategories.find((t=>t.id!==e.id&&t.label.toLowerCase()===d.toLowerCase()))){const e=(0,j.__)("This category already exists. Please use a different name.");return(0,Y.speak)(e,"assertive"),g(e),void l.current?.focus()}try{_(!0);const t=await y("taxonomy",R,{id:e.id,slug:e.slug,name:d});f("getUserPatternCategories"),s?.(t),n(),b((0,j.__)("Pattern category renamed."),{type:"snackbar",id:"pattern-category-update"})}catch(e){r?.(),x(e.message,{type:"snackbar",id:"pattern-category-update"})}finally{_(!1),u("")}}},children:(0,P.jsxs)(S.__experimentalVStack,{spacing:"5",children:[(0,P.jsxs)(S.__experimentalVStack,{spacing:"2",children:[(0,P.jsx)(S.TextControl,{ref:l,__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,j.__)("Name"),value:d,onChange:e=>{m&&g(void 0),u(e)},"aria-describedby":h,required:!0}),m&&(0,P.jsx)("span",{className:"patterns-rename-pattern-category-modal__validation-message",id:h,children:m})]}),(0,P.jsxs)(S.__experimentalHStack,{justify:"right",children:[(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:v,children:(0,j.__)("Cancel")}),(0,P.jsx)(S.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!d||d===e.name||p,isBusy:p,children:(0,j.__)("Save")})]})]})})})},PatternOverridesControls:W,ResetOverridesControl:function(e){const t=e.attributes.metadata?.name,n=(0,a.useRegistry)(),r=(0,a.useSelect)((n=>{if(!t)return;const{getBlockAttributes:r,getBlockParentsByBlockName:a}=n(c.store),[s]=a(e.clientId,"core/block",!0);if(!s)return;const o=r(s)[Z];return o?o.hasOwnProperty(t):void 0}),[e.clientId,t]);return(0,P.jsx)(c.__unstableBlockToolbarLastItem,{children:(0,P.jsx)(S.ToolbarGroup,{children:(0,P.jsx)(S.ToolbarButton,{onClick:function(){const{getBlockAttributes:r,getBlockParentsByBlockName:a}=n.select(c.store),[s]=a(e.clientId,"core/block",!0);if(!s)return;const o=r(s)[Z];if(!o.hasOwnProperty(t))return;const{updateBlockAttributes:i,__unstableMarkLastChangeAsPersistent:l}=n.dispatch(c.store);l();let d={...o};delete d[t],Object.keys(d).length||(d=void 0),i(s,{[Z]:d})},disabled:!r,children:(0,j.__)("Reset")})})})},PatternOverridesBlockControls:function(){const{clientIds:e,hasPatternOverrides:t,hasParentPattern:n}=(0,a.useSelect)((e=>{const{getBlockAttributes:t,getSelectedBlockClientIds:n,getBlockParentsByBlockName:r}=e(c.store),a=n(),s=a.every((e=>{var n;return Object.values(null!==(n=t(e)?.metadata?.bindings)&&void 0!==n?n:{}).some((e=>e?.source===_))})),o=a.every((e=>r(e,"core/block",!0).length>0));return{clientIds:a,hasPatternOverrides:s,hasParentPattern:o}}),[]);return t&&n?(0,P.jsx)(c.BlockControls,{group:"parent",children:(0,P.jsx)(K,{clientIds:e})}):null},useAddPatternCategory:O,PATTERN_TYPES:l,PATTERN_DEFAULT_CATEGORY:d,PATTERN_USER_CATEGORY:"my-patterns",EXCLUDED_PATTERN_SOURCES:["core","pattern-directory/core","pattern-directory/featured"],PATTERN_SYNC_TYPES:u,PARTIAL_SYNCING_SUPPORTED_BLOCKS:p}),(window.wp=window.wp||{}).patterns=t})();