var e={380:(e,t,n)=>{n.d(t,{zj:()=>ft,SD:()=>ve,V6:()=>ye,$K:()=>me,vT:()=>pt,jb:()=>Jt,yT:()=>we,M_:()=>dt,hb:()=>Te,vJ:()=>Ee,ip:()=>xe,Nf:()=>Oe,Kr:()=>Pe,li:()=>b,J0:()=>m,FH:()=>Se,v4:()=>ke});var r,o,i,s,a=n(622),u=0,l=[],c=a.fF,_=c.__b,f=c.__r,p=c.diffed,h=c.__c,d=c.unmount,v=c.__;function y(e,t){c.__h&&c.__h(o,e,u||t),u=0;var n=o.__H||(o.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function m(e){return u=1,function(e,t,n){var i=y(r++,2);if(i.t=e,!i.__c&&(i.__=[n?n(t):N(void 0,t),function(e){var t=i.__N?i.__N[0]:i.__[0],n=i.t(t,e);t!==n&&(i.__N=[n,i.__[1]],i.__c.setState({}))}],i.__c=o,!o.u)){var s=function(e,t,n){if(!i.__c.__H)return!0;var r=i.__c.__H.__.filter((function(e){return!!e.__c}));if(r.every((function(e){return!e.__N})))return!a||a.call(this,e,t,n);var o=i.__c.props!==e;return r.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),a&&a.call(this,e,t,n)||o};o.u=!0;var a=o.shouldComponentUpdate,u=o.componentWillUpdate;o.componentWillUpdate=function(e,t,n){if(this.__e){var r=a;a=void 0,s(e,t,n),a=r}u&&u.call(this,e,t,n)},o.shouldComponentUpdate=s}return i.__N||i.__}(N,e)}function g(e,t){var n=y(r++,3);!c.__s&&C(n.__H,t)&&(n.__=e,n.i=t,o.__H.__h.push(n))}function w(e,t){var n=y(r++,4);!c.__s&&C(n.__H,t)&&(n.__=e,n.i=t,o.__h.push(n))}function b(e){return u=5,k((function(){return{current:e}}),[])}function k(e,t){var n=y(r++,7);return C(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function S(e,t){return u=8,k((function(){return e}),t)}function x(e){var t=o.context[e.__c],n=y(r++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(o)),t.props.value):e.__}function E(){for(var e;e=l.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(P),e.__H.__h.forEach(F),e.__H.__h=[]}catch(t){e.__H.__h=[],c.__e(t,e.__v)}}c.__b=function(e){o=null,_&&_(e)},c.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),v&&v(e,t)},c.__r=function(e){f&&f(e),r=0;var t=(o=e.__c).__H;t&&(i===o?(t.__h=[],o.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0}))):(t.__h.forEach(P),t.__h.forEach(F),t.__h=[],r=0)),i=o},c.diffed=function(e){p&&p(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==l.push(t)&&s===c.requestAnimationFrame||((s=c.requestAnimationFrame)||T)(E)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.i=void 0}))),i=o=null},c.__c=function(e,t){t.some((function(e){try{e.__h.forEach(P),e.__h=e.__h.filter((function(e){return!e.__||F(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],c.__e(n,e.__v)}})),h&&h(e,t)},c.unmount=function(e){d&&d(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{P(e)}catch(e){t=e}})),n.__H=void 0,t&&c.__e(t,n.__v))};var O="function"==typeof requestAnimationFrame;function T(e){var t,n=function(){clearTimeout(r),O&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);O&&(t=requestAnimationFrame(n))}function P(e){var t=o,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),o=t}function F(e){var t=o;e.__c=e.__(),o=t}function C(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function N(e,t){return"function"==typeof t?t(e):t}var j=Symbol.for("preact-signals");function M(){if(W>1)W--;else{for(var e,t=!1;void 0!==A;){var n=A;for(A=void 0,L++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&V(n))try{n.c()}catch(n){t||(e=n,t=!0)}n=r}}if(L=0,W--,t)throw e}}function $(e){if(W>0)return e();W++;try{return e()}finally{M()}}var H=void 0;var U,A=void 0,W=0,L=0,D=0;function I(e){if(void 0!==H){var t=e.n;if(void 0===t||t.t!==H)return t={i:0,S:e,p:H.s,n:void 0,t:H,e:void 0,x:void 0,r:t},void 0!==H.s&&(H.s.n=t),H.s=t,e.n=t,32&H.f&&e.S(t),t;if(-1===t.i)return t.i=0,void 0!==t.n&&(t.n.p=t.p,void 0!==t.p&&(t.p.n=t.n),t.p=H.s,t.n=void 0,H.s.n=t,H.s=t),t}}function R(e){this.v=e,this.i=0,this.n=void 0,this.t=void 0}function z(e){return new R(e)}function V(e){for(var t=e.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function B(e){for(var t=e.s;void 0!==t;t=t.n){var n=t.S.n;if(void 0!==n&&(t.r=n),t.S.n=t,t.i=-1,void 0===t.n){e.s=t;break}}}function J(e){for(var t=e.s,n=void 0;void 0!==t;){var r=t.p;-1===t.i?(t.S.U(t),void 0!==r&&(r.n=t.n),void 0!==t.n&&(t.n.p=r)):n=t,t.S.n=t.r,void 0!==t.r&&(t.r=void 0),t=r}e.s=n}function K(e){R.call(this,void 0),this.x=e,this.s=void 0,this.g=D-1,this.f=4}function q(e){return new K(e)}function Y(e){var t=e.u;if(e.u=void 0,"function"==typeof t){W++;var n=H;H=void 0;try{t()}catch(t){throw e.f&=-2,e.f|=8,X(e),t}finally{H=n,M()}}}function X(e){for(var t=e.s;void 0!==t;t=t.n)t.S.U(t);e.x=void 0,e.s=void 0,Y(e)}function G(e){if(H!==this)throw new Error("Out-of-order effect");J(this),H=e,this.f&=-2,8&this.f&&X(this),M()}function Q(e){this.x=e,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function Z(e){var t=new Q(e);try{t.c()}catch(e){throw t.d(),e}return t.d.bind(t)}function ee(e,t){a.fF[e]=t.bind(null,a.fF[e]||function(){})}function te(e){U&&U(),U=e&&e.S()}function ne(e){var t=this,n=e.data,r=function(e){return k((function(){return z(e)}),[])}(n);r.value=n;var o=k((function(){for(var e=t.__v;e=e.__;)if(e.__c){e.__c.__$f|=4;break}return t.__$u.c=function(){var e,n=t.__$u.S(),r=o.value;n(),(0,a.zO)(r)||3!==(null==(e=t.base)?void 0:e.nodeType)?(t.__$f|=1,t.setState({})):t.base.data=r},q((function(){var e=r.value.value;return 0===e?0:!0===e?"":e||""}))}),[]);return o.value}function re(e,t,n,r){var o=t in e&&void 0===e.ownerSVGElement,i=z(n);return{o:function(e,t){i.value=e,r=t},d:Z((function(){var n=i.value.value;r[t]!==n&&(r[t]=n,o?e[t]=n:n?e.setAttribute(t,n):e.removeAttribute(t))}))}}R.prototype.brand=j,R.prototype.h=function(){return!0},R.prototype.S=function(e){this.t!==e&&void 0===e.e&&(e.x=this.t,void 0!==this.t&&(this.t.e=e),this.t=e)},R.prototype.U=function(e){if(void 0!==this.t){var t=e.e,n=e.x;void 0!==t&&(t.x=n,e.e=void 0),void 0!==n&&(n.e=t,e.x=void 0),e===this.t&&(this.t=n)}},R.prototype.subscribe=function(e){var t=this;return Z((function(){var n=t.value,r=H;H=void 0;try{e(n)}finally{H=r}}))},R.prototype.valueOf=function(){return this.value},R.prototype.toString=function(){return this.value+""},R.prototype.toJSON=function(){return this.value},R.prototype.peek=function(){var e=H;H=void 0;try{return this.value}finally{H=e}},Object.defineProperty(R.prototype,"value",{get:function(){var e=I(this);return void 0!==e&&(e.i=this.i),this.v},set:function(e){if(e!==this.v){if(L>100)throw new Error("Cycle detected");this.v=e,this.i++,D++,W++;try{for(var t=this.t;void 0!==t;t=t.x)t.t.N()}finally{M()}}}}),(K.prototype=new R).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===D)return!0;if(this.g=D,this.f|=1,this.i>0&&!V(this))return this.f&=-2,!0;var e=H;try{B(this),H=this;var t=this.x();(16&this.f||this.v!==t||0===this.i)&&(this.v=t,this.f&=-17,this.i++)}catch(e){this.v=e,this.f|=16,this.i++}return H=e,J(this),this.f&=-2,!0},K.prototype.S=function(e){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}R.prototype.S.call(this,e)},K.prototype.U=function(e){if(void 0!==this.t&&(R.prototype.U.call(this,e),void 0===this.t)){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}},K.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var e=this.t;void 0!==e;e=e.x)e.t.N()}},Object.defineProperty(K.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var e=I(this);if(this.h(),void 0!==e&&(e.i=this.i),16&this.f)throw this.v;return this.v}}),Q.prototype.c=function(){var e=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();"function"==typeof t&&(this.u=t)}finally{e()}},Q.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,Y(this),B(this),W++;var e=H;return H=this,G.bind(this,e)},Q.prototype.N=function(){2&this.f||(this.f|=2,this.o=A,A=this)},Q.prototype.d=function(){this.f|=8,1&this.f||X(this)},ne.displayName="_st",Object.defineProperties(R.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:ne},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),ee("__b",(function(e,t){if("string"==typeof t.type){var n,r=t.props;for(var o in r)if("children"!==o){var i=r[o];i instanceof R&&(n||(t.__np=n={}),n[o]=i,r[o]=i.peek())}}e(t)})),ee("__r",(function(e,t){te();var n,r=t.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=n=function(e){var t;return Z((function(){t=this})),t.c=function(){r.__$f|=1,r.setState({})},t}())),r,te(n),e(t)})),ee("__e",(function(e,t,n,r){te(),void 0,e(t,n,r)})),ee("diffed",(function(e,t){var n;if(te(),void 0,"string"==typeof t.type&&(n=t.__e)){var r=t.__np,o=t.props;if(r){var i=n.U;if(i)for(var s in i){var a=i[s];void 0===a||s in r||(a.d(),i[s]=void 0)}else n.U=i={};for(var u in r){var l=i[u],c=r[u];void 0===l?(l=re(n,u,c,o),i[u]=l):l.o(c,o)}}}e(t)})),ee("unmount",(function(e,t){if("string"==typeof t.type){var n=t.__e;if(n){var r=n.U;if(r)for(var o in n.U=void 0,r){var i=r[o];i&&i.d()}}}else{var s=t.__c;if(s){var a=s.__$u;a&&(s.__$u=void 0,a.d())}}e(t)})),ee("__h",(function(e,t,n,r){(r<3||9===r)&&(t.__$f|=2),e(t,n,r)})),a.uA.prototype.shouldComponentUpdate=function(e,t){var n=this.__$u,r=n&&void 0!==n.s;for(var o in t)return!0;if(this.__f||"boolean"==typeof this.u&&!0===this.u){if(!(r||2&this.__$f||4&this.__$f))return!0;if(1&this.__$f)return!0}else{if(!(r||4&this.__$f))return!0;if(3&this.__$f)return!0}for(var i in e)if("__source"!==i&&e[i]!==this.props[i])return!0;for(var s in this.props)if(!(s in e))return!0;return!1};const oe=[],ie=()=>oe.slice(-1)[0],se=e=>{oe.push(e)},ae=()=>{oe.pop()},ue=[],le=()=>ue.slice(-1)[0],ce=e=>{ue.push(e)},_e=()=>{ue.pop()},fe=new WeakMap,pe=()=>{throw new Error("Please use `data-wp-bind` to modify the attributes of an element.")},he={get(e,t,n){const r=Reflect.get(e,t,n);return r&&"object"==typeof r?de(r):r},set:pe,deleteProperty:pe},de=e=>(fe.has(e)||fe.set(e,new Proxy(e,he)),fe.get(e)),ve=e=>le().context[e||ie()],ye=()=>{const e=le();const{ref:t,attributes:n}=e;return Object.freeze({ref:t.current,attributes:de(n)})},me=e=>le().serverContext[e||ie()],ge=e=>new Promise((t=>{const n=()=>{clearTimeout(r),window.cancelAnimationFrame(o),setTimeout((()=>{e(),t()}))},r=setTimeout(n,100),o=window.requestAnimationFrame(n)})),we=()=>new Promise((e=>{setTimeout(e,0)}));function be(e){g((()=>{let t=null,n=!1;return t=function(e,t){let n=()=>{};const r=Z((function(){return n=this.c.bind(this),this.x=e,this.c=t,e()}));return{flush:n,dispose:r}}(e,(async()=>{t&&!n&&(n=!0,await ge(t.flush),n=!1)})),t.dispose}),[])}function ke(e){const t=le(),n=ie();return"GeneratorFunction"===e?.constructor?.name?async(...r)=>{const o=e(...r);let i,s;for(;;){se(n),ce(t);try{s=o.next(i)}finally{_e(),ae()}try{i=await s.value}catch(e){se(n),ce(t),o.throw(e)}finally{_e(),ae()}if(s.done)break}return i}:(...r)=>{se(n),ce(t);try{return e(...r)}finally{ae(),_e()}}}function Se(e){be(ke(e))}function xe(e){g(ke(e),[])}function Ee(e,t){g(ke(e),t)}function Oe(e,t){w(ke(e),t)}function Te(e,t){return S(ke(e),t)}function Pe(e,t){return k(ke(e),t)}new Set;const Fe=e=>{0},Ce=e=>Boolean(e&&"object"==typeof e&&e.constructor===Object),Ne=new WeakMap,je=new WeakMap,Me=new WeakMap,$e=new Set([Object,Array]),He=(e,t,n)=>{if(!We(t))throw Error("This object cannot be proxified.");if(!Ne.has(t)){const r=new Proxy(t,n);Ne.set(t,r),je.set(r,t),Me.set(r,e)}return Ne.get(t)},Ue=e=>Ne.get(e),Ae=e=>Me.get(e),We=e=>"object"==typeof e&&null!==e&&(!Me.has(e)&&$e.has(e.constructor)),Le={};class De{constructor(e){this.owner=e,this.computedsByScope=new WeakMap}setValue(e){this.update({value:e})}setGetter(e){this.update({get:e})}getComputed(){const e=le()||Le;if(this.valueSignal||this.getterSignal||this.update({}),!this.computedsByScope.has(e)){const t=()=>{const e=this.getterSignal?.value;return e?e.call(this.owner):this.valueSignal?.value};se(Ae(this.owner)),this.computedsByScope.set(e,q(ke(t))),ae()}return this.computedsByScope.get(e)}update({get:e,value:t}){this.valueSignal?t===this.valueSignal.peek()&&e===this.getterSignal.peek()||$((()=>{this.valueSignal.value=t,this.getterSignal.value=e})):(this.valueSignal=z(t),this.getterSignal=z(e))}}const Ie=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter((e=>"symbol"==typeof e))),Re=new WeakMap,ze=(e,t)=>Re.has(e)&&Re.get(e).has(t),Ve=new WeakSet,Be=(e,t,n)=>{Re.has(e)||Re.set(e,new Map),t="number"==typeof t?`${t}`:t;const r=Re.get(e);if(!r.has(t)){const o=Ae(e),i=new De(e);if(r.set(t,i),n){const{get:t,value:r}=n;if(t)i.setGetter(t);else{const t=Ve.has(e);i.setValue(We(r)?Ye(o,r,{readOnly:t}):r)}}}return r.get(t)},Je=new WeakMap;let Ke=!1;const qe={get(e,t,n){if(Ke||!e.hasOwnProperty(t)&&t in e||"symbol"==typeof t&&Ie.has(t))return Reflect.get(e,t,n);const r=Object.getOwnPropertyDescriptor(e,t),o=Be(n,t,r).getComputed().value;if("function"==typeof o){const e=Ae(n);return(...t)=>{se(e);try{return o.call(n,...t)}finally{ae()}}}return o},set(e,t,n,r){if(Ve.has(r))return!1;se(Ae(r));try{return Reflect.set(e,t,n,r)}finally{ae()}},defineProperty(e,t,n){if(Ve.has(Ue(e)))return!1;const r=!(t in e),o=Reflect.defineProperty(e,t,n);if(o){const o=Ue(e),i=Be(o,t),{get:s,value:a}=n;if(s)i.setGetter(s);else{const e=Ae(o);i.setValue(We(a)?Ye(e,a):a)}if(r&&Je.has(e)&&Je.get(e).value++,Array.isArray(e)&&Re.get(o)?.has("length")){Be(o,"length").setValue(e.length)}}return o},deleteProperty(e,t){if(Ve.has(Ue(e)))return!1;const n=Reflect.deleteProperty(e,t);if(n){Be(Ue(e),t).setValue(void 0),Je.has(e)&&Je.get(e).value++}return n},ownKeys:e=>(Je.has(e)||Je.set(e,z(0)),Je._=Je.get(e).value,Reflect.ownKeys(e))},Ye=(e,t,n)=>{const r=He(e,t,qe);return n?.readOnly&&Ve.add(r),r},Xe=(e,t,n=!0)=>{if(!Ce(e)||!Ce(t))return;let r=!1;for(const o in t){const i=!(o in e);r=r||i;const s=Object.getOwnPropertyDescriptor(t,o),a=Ue(e),u=!!a&&ze(a,o)&&Be(a,o);if("function"==typeof s.get||"function"==typeof s.set)(n||i)&&(Object.defineProperty(e,o,{...s,configurable:!0,enumerable:!0}),s.get&&u&&u.setGetter(s.get));else if(Ce(t[o])){const r=Object.getOwnPropertyDescriptor(e,o)?.value;if(i||n&&!Ce(r)){if(e[o]={},u){const t=Ae(a);u.setValue(Ye(t,e[o]))}Xe(e[o],t[o],n)}else Ce(r)&&Xe(e[o],t[o],n)}else if((n||i)&&(Object.defineProperty(e,o,s),u)){const{value:e}=s,t=Ae(a);u.setValue(We(e)?Ye(t,e):e)}}r&&Je.has(e)&&Je.get(e).value++},Ge=(e,t,n=!0)=>$((()=>{return Xe((r=e,je.get(r)||e),t,n);var r})),Qe=new WeakSet,Ze={get:(e,t,n)=>{const r=Reflect.get(e,t),o=Ae(n);if(void 0===r&&Qe.has(n)){const n={};return Reflect.set(e,t,n),et(o,n,!1)}if("function"==typeof r){se(o);const e=ke(r);return ae(),e}return Ce(r)&&We(r)?et(o,r,!1):r}},et=(e,t,n=!0)=>{const r=He(e,t,Ze);return r&&n&&Qe.add(r),r},tt=new WeakMap,nt=new WeakMap,rt=new WeakSet,ot=Reflect.getOwnPropertyDescriptor,it={get:(e,t)=>{const n=nt.get(e),r=e[t];return t in e?r:n[t]},set:(e,t,n)=>{const r=nt.get(e);return(t in e||!(t in r)?e:r)[t]=n,!0},ownKeys:e=>[...new Set([...Object.keys(nt.get(e)),...Object.keys(e)])],getOwnPropertyDescriptor:(e,t)=>ot(e,t)||ot(nt.get(e),t),has:(e,t)=>Reflect.has(e,t)||Reflect.has(nt.get(e),t)},st=(e,t={})=>{if(rt.has(e))throw Error("This object cannot be proxified.");if(nt.set(e,t),!tt.has(e)){const t=new Proxy(e,it);tt.set(e,t),rt.add(t)}return tt.get(e)},at=new Map,ut=new Map,lt=new Map,ct=new Map,_t=new Map,ft=e=>ct.get(e||ie())||{},pt=e=>{const t=e||ie();return _t.has(t)||_t.set(t,Ye(t,{},{readOnly:!0})),_t.get(t)},ht="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";function dt(e,{state:t={},...n}={},{lock:r=!1}={}){if(at.has(e)){if(r===ht||lt.has(e)){const t=lt.get(e);if(!(r===ht||!0!==r&&r===t))throw t?Error("Cannot unlock a private store with an invalid lock code"):Error("Cannot lock a public store")}else lt.set(e,r);const o=ut.get(e);Ge(o,n),Ge(o.state,t)}else{r!==ht&&lt.set(e,r);const o={state:Ye(e,Ce(t)?t:{}),...n},i=et(e,o);ut.set(e,o),at.set(e,i)}return at.get(e)}const vt=(e=document)=>{var t;const n=null!==(t=e.getElementById("wp-script-module-data-@wordpress/interactivity"))&&void 0!==t?t:e.getElementById("wp-interactivity-data");if(n?.textContent)try{return JSON.parse(n.textContent)}catch{}return{}},yt=e=>{Ce(e?.state)&&Object.entries(e.state).forEach((([e,t])=>{const n=dt(e,{},{lock:ht});Ge(n.state,t,!1),Ge(pt(e),t)})),Ce(e?.config)&&Object.entries(e.config).forEach((([e,t])=>{ct.set(e,t)}))},mt=vt();function gt(e){return null!==e.suffix}function wt(e){return null===e.suffix}yt(mt);const bt=(0,a.q6)({client:{},server:{}}),kt={},St={},xt=(e,t,{priority:n=10}={})=>{kt[e]=t,St[e]=n},Et=({scope:e})=>(t,...n)=>{let{value:r,namespace:o}=t;if("string"!=typeof r)throw new Error("The `value` prop should be a string path");const i="!"===r[0]&&!!(r=r.slice(1));ce(e);const s=((e,t)=>{if(!t)return void Fe();let n=at.get(t);void 0===n&&(n=dt(t,void 0,{lock:ht}));const r={...n,context:le().context[t]};try{return e.split(".").reduce(((e,t)=>e[t]),r)}catch(e){}})(r,o),a="function"==typeof s?s(...n):s;return _e(),i?!a:a},Ot=({directives:e,priorityLevels:[t,...n],element:r,originalProps:o,previousScope:i})=>{const s=b({}).current;s.evaluate=S(Et({scope:s}),[]);const{client:u,server:l}=x(bt);s.context=u,s.serverContext=l,s.ref=i?.ref||b(null),r=(0,a.Ob)(r,{ref:s.ref}),s.attributes=r.props;const c=n.length>0?(0,a.h)(Ot,{directives:e,priorityLevels:n,element:r,originalProps:o,previousScope:s}):r,_={...o,children:c},f={directives:e,props:_,element:r,context:bt,evaluate:s.evaluate};ce(s);for(const e of t){const t=kt[e]?.(f);void 0!==t&&(_.children=t)}return _e(),_.children},Tt=a.fF.vnode;function Pt(e){return Ce(e)?Object.fromEntries(Object.entries(e).map((([e,t])=>[e,Pt(t)]))):Array.isArray(e)?e.map((e=>Pt(e))):e}a.fF.vnode=e=>{if(e.props.__directives){const t=e.props,n=t.__directives;n.key&&(e.key=n.key.find(wt).value),delete t.__directives;const r=(e=>{const t=Object.keys(e).reduce(((e,t)=>{if(kt[t]){const n=St[t];(e[n]=e[n]||[]).push(t)}return e}),{});return Object.entries(t).sort((([e],[t])=>parseInt(e)-parseInt(t))).map((([,e])=>e))})(n);r.length>0&&(e.props={directives:n,priorityLevels:r,originalProps:t,type:e.type,element:(0,a.h)(e.type,t),top:!0},e.type=Ot)}Tt&&Tt(e)};const Ft=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Ct=/\/\*[^]*?\*\/|  +/g,Nt=/\n+/g,jt=e=>({directives:t,evaluate:n})=>{t[`on-${e}`].filter(gt).forEach((t=>{const r=t.suffix.split("--",1)[0];xe((()=>{const o=e=>n(t,e),i="window"===e?window:document;return i.addEventListener(r,o),()=>i.removeEventListener(r,o)}))}))},Mt=e=>({directives:t,evaluate:n})=>{t[`on-async-${e}`].filter(gt).forEach((t=>{const r=t.suffix.split("--",1)[0];xe((()=>{const o=async e=>{await we(),n(t,e)},i="window"===e?window:document;return i.addEventListener(r,o,{passive:!0}),()=>i.removeEventListener(r,o)}))}))},$t="wp",Ht=`data-${$t}-ignore`,Ut=`data-${$t}-interactive`,At=`data-${$t}-`,Wt=[],Lt=new RegExp(`^data-${$t}-([a-z0-9]+(?:-[a-z0-9]+)*)(?:--([a-z0-9_-]+))?$`,"i"),Dt=/^([\w_\/-]+)::(.+)$/,It=new WeakSet;function Rt(e){const t=document.createTreeWalker(e,205);return function e(n){const{nodeType:r}=n;if(3===r)return[n.data];if(4===r){var o;const e=t.nextSibling();return n.replaceWith(new window.Text(null!==(o=n.nodeValue)&&void 0!==o?o:"")),[n.nodeValue,e]}if(8===r||7===r){const e=t.nextSibling();return n.remove(),[null,e]}const i=n,{attributes:s}=i,u=i.localName,l={},c=[],_=[];let f=!1,p=!1;for(let e=0;e<s.length;e++){const t=s[e].name,n=s[e].value;if(t[At.length]&&t.slice(0,At.length)===At)if(t===Ht)f=!0;else{var h,d;const e=Dt.exec(n),r=null!==(h=e?.[1])&&void 0!==h?h:null;let o=null!==(d=e?.[2])&&void 0!==d?d:n;try{const e=JSON.parse(o);v=e,o=Boolean(v&&"object"==typeof v&&v.constructor===Object)?e:o}catch{}if(t===Ut){p=!0;const e="string"==typeof o?o:"string"==typeof o?.namespace?o.namespace:null;Wt.push(e)}else _.push([t,r,o])}else if("ref"===t)continue;l[t]=n}var v;if(f&&!p)return[(0,a.h)(u,{...l,innerHTML:i.innerHTML,__directives:{ignore:!0}})];if(p&&It.add(i),_.length&&(l.__directives=_.reduce(((e,[t,n,r])=>{const o=Lt.exec(t);if(null===o)return Fe(),e;const i=o[1]||"",s=o[2]||null;var a;return e[i]=e[i]||[],e[i].push({namespace:null!=n?n:null!==(a=Wt[Wt.length-1])&&void 0!==a?a:null,value:r,suffix:s}),e}),{})),"template"===u)l.content=[...i.content.childNodes].map((e=>Rt(e)));else{let n=t.firstChild();if(n){for(;n;){const[r,o]=e(n);r&&c.push(r),n=o||t.nextSibling()}t.parentNode()}}return p&&Wt.pop(),[(0,a.h)(u,l,c)]}(t.currentNode)}const zt=new WeakMap,Vt=e=>{if(!e.parentElement)throw Error("The passed region should be an element with a parent.");return zt.has(e)||zt.set(e,((e,t)=>{const n=(t=[].concat(t))[t.length-1].nextSibling;function r(t,r){e.insertBefore(t,r||n)}return e.__k={nodeType:1,parentNode:e,firstChild:t[0],childNodes:t,insertBefore:r,appendChild:r,removeChild(t){e.removeChild(t)}}})(e.parentElement,e)),zt.get(e)},Bt=new WeakMap,Jt=e=>{if("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."===e)return{directivePrefix:$t,getRegionRootFragment:Vt,initialVdom:Bt,toVdom:Rt,directive:xt,getNamespace:ie,h:a.h,cloneElement:a.Ob,render:a.XX,proxifyState:Ye,parseServerData:vt,populateServerData:yt,batch:$};throw new Error("Forbidden access.")};xt("context",(({directives:{context:e},props:{children:t},context:n})=>{const{Provider:r}=n,o=e.find(wt),{client:i,server:s}=x(n),u=o.namespace,l=b(Ye(u,{})),c=b(Ye(u,{},{readOnly:!0})),_=k((()=>{const e={client:{...i},server:{...s}};if(o){const{namespace:t,value:n}=o;Ce(n)||Fe(),Ge(l.current,Pt(n),!1),Ge(c.current,Pt(n)),e.client[t]=st(l.current,i[t]),e.server[t]=st(c.current,s[t])}return e}),[o,i,s]);return(0,a.h)(r,{value:_},t)}),{priority:5}),xt("watch",(({directives:{watch:e},evaluate:t})=>{e.forEach((e=>{Se((()=>t(e)))}))})),xt("init",(({directives:{init:e},evaluate:t})=>{e.forEach((e=>{xe((()=>t(e)))}))})),xt("on",(({directives:{on:e},element:t,evaluate:n})=>{const r=new Map;e.filter(gt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{e.forEach((e=>{o&&o(t),n(e,t)}))}}))})),xt("on-async",(({directives:{"on-async":e},element:t,evaluate:n})=>{const r=new Map;e.filter(gt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{o&&o(t),e.forEach((async e=>{await we(),n(e,t)}))}}))})),xt("on-window",jt("window")),xt("on-document",jt("document")),xt("on-async-window",Mt("window")),xt("on-async-document",Mt("document")),xt("class",(({directives:{class:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e),i=t.props.class||"",s=new RegExp(`(^|\\s)${r}(\\s|$)`,"g");o?s.test(i)||(t.props.class=i?`${i} ${r}`:r):t.props.class=i.replace(s," ").trim(),xe((()=>{o?t.ref.current.classList.add(r):t.ref.current.classList.remove(r)}))}))})),xt("style",(({directives:{style:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e);t.props.style=t.props.style||{},"string"==typeof t.props.style&&(t.props.style=(e=>{const t=[{}];let n,r;for(;n=Ft.exec(e.replace(Ct,""));)n[4]?t.shift():n[3]?(r=n[3].replace(Nt," ").trim(),t.unshift(t[0][r]=t[0][r]||{})):t[0][n[1]]=n[2].replace(Nt," ").trim();return t[0]})(t.props.style)),o?t.props.style[r]=o:delete t.props.style[r],xe((()=>{o?t.ref.current.style[r]=o:t.ref.current.style.removeProperty(r)}))}))})),xt("bind",(({directives:{bind:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e);t.props[r]=o,xe((()=>{const e=t.ref.current;if("style"!==r){if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in e)try{return void(e[r]=null==o?"":o)}catch(e){}null==o||!1===o&&"-"!==r[4]?e.removeAttribute(r):e.setAttribute(r,o)}else"string"==typeof o&&(e.style.cssText=o)}))}))})),xt("ignore",(({element:{type:e,props:{innerHTML:t,...n}}})=>{const r=k((()=>t),[]);return(0,a.h)(e,{dangerouslySetInnerHTML:{__html:r},...n})})),xt("text",(({directives:{text:e},element:t,evaluate:n})=>{const r=e.find(wt);if(r)try{const e=n(r);t.props.children="object"==typeof e?null:e.toString()}catch(e){t.props.children=null}else t.props.children=null})),xt("run",(({directives:{run:e},evaluate:t})=>{e.forEach((e=>t(e)))})),xt("each",(({directives:{each:e,"each-key":t},context:n,element:r,evaluate:o})=>{if("template"!==r.type)return;const{Provider:i}=n,s=x(n),[u]=e,{namespace:l}=u,c=o(u),_=gt(u)?u.suffix.replace(/^-+|-+$/g,"").toLowerCase().replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()})):"item";return c.map((e=>{const n=st(Ye(l,{}),s.client[l]),o={client:{...s.client,[l]:n},server:{...s.server}};o.client[l][_]=e;const u={...le(),context:o.client,serverContext:o.server},c=t?Et({scope:u})(t[0]):e;return(0,a.h)(i,{value:o,key:c},r.props.content)}))}),{priority:20}),xt("each-child",(()=>null),{priority:1}),(async()=>{const e=document.querySelectorAll(`[data-${$t}-interactive]`);for(const t of e)if(!It.has(t)){await we();const e=Vt(t),n=Rt(t);Bt.set(t,n),await we(),(0,a.Qv)(n,e)}})()},622:(e,t,n)=>{n.d(t,{FK:()=>x,Ob:()=>B,Qv:()=>V,XX:()=>z,fF:()=>o,h:()=>k,q6:()=>J,uA:()=>E,zO:()=>s});var r,o,i,s,a,u,l,c,_,f,p,h,d,v={},y=[],m=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,g=Array.isArray;function w(e,t){for(var n in t)e[n]=t[n];return e}function b(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function k(e,t,n){var o,i,s,a={};for(s in t)"key"==s?o=t[s]:"ref"==s?i=t[s]:a[s]=t[s];if(arguments.length>2&&(a.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return S(e,a,o,i,null)}function S(e,t,n,r,s){var a={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==s?++i:s,__i:-1,__u:0};return null==s&&null!=o.vnode&&o.vnode(a),a}function x(e){return e.children}function E(e,t){this.props=e,this.context=t}function O(e,t){if(null==t)return e.__?O(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?O(e):null}function T(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return T(e)}}function P(e){(!e.__d&&(e.__d=!0)&&a.push(e)&&!F.__r++||u!==o.debounceRendering)&&((u=o.debounceRendering)||l)(F)}function F(){var e,t,n,r,i,s,u,l;for(a.sort(c);e=a.shift();)e.__d&&(t=a.length,r=void 0,s=(i=(n=e).__v).__e,u=[],l=[],n.__P&&((r=w({},i)).__v=i.__v+1,o.vnode&&o.vnode(r),A(n.__P,r,i,n.__n,n.__P.namespaceURI,32&i.__u?[s]:null,u,null==s?O(i):s,!!(32&i.__u),l),r.__v=i.__v,r.__.__k[r.__i]=r,W(u,r,l),r.__e!=s&&T(r)),a.length>t&&a.sort(c));F.__r=0}function C(e,t,n,r,o,i,s,a,u,l,c){var _,f,p,h,d,m,g=r&&r.__k||y,w=t.length;for(u=N(n,t,g,u,w),_=0;_<w;_++)null!=(p=n.__k[_])&&(f=-1===p.__i?v:g[p.__i]||v,p.__i=_,m=A(e,p,f,o,i,s,a,u,l,c),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&D(f.ref,null,p),c.push(p.ref,p.__c||h,p)),null==d&&null!=h&&(d=h),4&p.__u||f.__k===p.__k?u=j(p,u,e):"function"==typeof p.type&&void 0!==m?u=m:h&&(u=h.nextSibling),p.__u&=-7);return n.__e=d,u}function N(e,t,n,r,o){var i,s,a,u,l,c=n.length,_=c,f=0;for(e.__k=new Array(o),i=0;i<o;i++)null!=(s=t[i])&&"boolean"!=typeof s&&"function"!=typeof s?(u=i+f,(s=e.__k[i]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?S(null,s,null,null,null):g(s)?S(x,{children:s},null,null,null):void 0===s.constructor&&s.__b>0?S(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=e,s.__b=e.__b+1,a=null,-1!==(l=s.__i=M(s,n,u,_))&&(_--,(a=n[l])&&(a.__u|=2)),null==a||null===a.__v?(-1==l&&f--,"function"!=typeof s.type&&(s.__u|=4)):l!=u&&(l==u-1?f--:l==u+1?f++:(l>u?f--:f++,s.__u|=4))):e.__k[i]=null;if(_)for(i=0;i<c;i++)null!=(a=n[i])&&0==(2&a.__u)&&(a.__e==r&&(r=O(a)),I(a,a));return r}function j(e,t,n){var r,o;if("function"==typeof e.type){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=j(r[o],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=O(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function M(e,t,n,r){var o,i,s=e.key,a=e.type,u=t[n];if(null===u||u&&s==u.key&&a===u.type&&0==(2&u.__u))return n;if(r>(null!=u&&0==(2&u.__u)?1:0))for(o=n-1,i=n+1;o>=0||i<t.length;){if(o>=0){if((u=t[o])&&0==(2&u.__u)&&s==u.key&&a===u.type)return o;o--}if(i<t.length){if((u=t[i])&&0==(2&u.__u)&&s==u.key&&a===u.type)return i;i++}}return-1}function $(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||m.test(t)?n:n+"px"}function H(e,t,n,r,o){var i;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||$(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||$(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(_,"$1")),t=t.toLowerCase()in e||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=f,e.addEventListener(t,i?h:p,i)):e.removeEventListener(t,i?h:p,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function U(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=f++;else if(t.t<n.u)return;return n(o.event?o.event(t):t)}}}function A(e,t,n,r,i,s,a,u,l,c){var _,f,p,h,d,v,y,m,k,S,O,T,P,F,N,j,M,$=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(l=!!(32&n.__u),s=[u=t.__e=n.__e]),(_=o.__b)&&_(t);e:if("function"==typeof $)try{if(m=t.props,k="prototype"in $&&$.prototype.render,S=(_=$.contextType)&&r[_.__c],O=_?S?S.props.value:_.__:r,n.__c?y=(f=t.__c=n.__c).__=f.__E:(k?t.__c=f=new $(m,O):(t.__c=f=new E(m,O),f.constructor=$,f.render=R),S&&S.sub(f),f.props=m,f.state||(f.state={}),f.context=O,f.__n=r,p=f.__d=!0,f.__h=[],f._sb=[]),k&&null==f.__s&&(f.__s=f.state),k&&null!=$.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=w({},f.__s)),w(f.__s,$.getDerivedStateFromProps(m,f.__s))),h=f.props,d=f.state,f.__v=t,p)k&&null==$.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),k&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(k&&null==$.getDerivedStateFromProps&&m!==h&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(m,O),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(m,f.__s,O)||t.__v==n.__v)){for(t.__v!=n.__v&&(f.props=m,f.state=f.__s,f.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),T=0;T<f._sb.length;T++)f.__h.push(f._sb[T]);f._sb=[],f.__h.length&&a.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(m,f.__s,O),k&&null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(h,d,v)}))}if(f.context=O,f.props=m,f.__P=e,f.__e=!1,P=o.__r,F=0,k){for(f.state=f.__s,f.__d=!1,P&&P(t),_=f.render(f.props,f.state,f.context),N=0;N<f._sb.length;N++)f.__h.push(f._sb[N]);f._sb=[]}else do{f.__d=!1,P&&P(t),_=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++F<25);f.state=f.__s,null!=f.getChildContext&&(r=w(w({},r),f.getChildContext())),k&&!p&&null!=f.getSnapshotBeforeUpdate&&(v=f.getSnapshotBeforeUpdate(h,d)),u=C(e,g(j=null!=_&&_.type===x&&null==_.key?_.props.children:_)?j:[j],t,n,r,i,s,a,u,l,c),f.base=t.__e,t.__u&=-161,f.__h.length&&a.push(f),y&&(f.__E=f.__=null)}catch(e){if(t.__v=null,l||null!=s)if(e.then){for(t.__u|=l?160:128;u&&8==u.nodeType&&u.nextSibling;)u=u.nextSibling;s[s.indexOf(u)]=null,t.__e=u}else for(M=s.length;M--;)b(s[M]);else t.__e=n.__e,t.__k=n.__k;o.__e(e,t,n)}else null==s&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):u=t.__e=L(n.__e,t,n,r,i,s,a,l,c);return(_=o.diffed)&&_(t),128&t.__u?void 0:u}function W(e,t,n){for(var r=0;r<n.length;r++)D(n[r],n[++r],n[++r]);o.__c&&o.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){o.__e(e,t.__v)}}))}function L(e,t,n,i,s,a,u,l,c){var _,f,p,h,d,y,m,w=n.props,k=t.props,S=t.type;if("svg"==S?s="http://www.w3.org/2000/svg":"math"==S?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=a)for(_=0;_<a.length;_++)if((d=a[_])&&"setAttribute"in d==!!S&&(S?d.localName==S:3==d.nodeType)){e=d,a[_]=null;break}if(null==e){if(null==S)return document.createTextNode(k);e=document.createElementNS(s,S,k.is&&k),l&&(o.__m&&o.__m(t,a),l=!1),a=null}if(null===S)w===k||l&&e.data===k||(e.data=k);else{if(a=a&&r.call(e.childNodes),w=n.props||v,!l&&null!=a)for(w={},_=0;_<e.attributes.length;_++)w[(d=e.attributes[_]).name]=d.value;for(_ in w)if(d=w[_],"children"==_);else if("dangerouslySetInnerHTML"==_)p=d;else if(!(_ in k)){if("value"==_&&"defaultValue"in k||"checked"==_&&"defaultChecked"in k)continue;H(e,_,null,d,s)}for(_ in k)d=k[_],"children"==_?h=d:"dangerouslySetInnerHTML"==_?f=d:"value"==_?y=d:"checked"==_?m=d:l&&"function"!=typeof d||w[_]===d||H(e,_,d,w[_],s);if(f)l||p&&(f.__html===p.__html||f.__html===e.innerHTML)||(e.innerHTML=f.__html),t.__k=[];else if(p&&(e.innerHTML=""),C(e,g(h)?h:[h],t,n,i,"foreignObject"==S?"http://www.w3.org/1999/xhtml":s,a,u,a?a[0]:n.__k&&O(n,0),l,c),null!=a)for(_=a.length;_--;)b(a[_]);l||(_="value","progress"==S&&null==y?e.removeAttribute("value"):void 0!==y&&(y!==e[_]||"progress"==S&&!y||"option"==S&&y!==w[_])&&H(e,_,y,w[_],s),_="checked",void 0!==m&&m!==e[_]&&H(e,_,m,w[_],s))}return e}function D(e,t,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==t||(e.__u=e(t))}else e.current=t}catch(e){o.__e(e,n)}}function I(e,t,n){var r,i;if(o.unmount&&o.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||D(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){o.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&I(r[i],t,n||"function"!=typeof e.type);n||b(e.__e),e.__c=e.__=e.__e=void 0}function R(e,t,n){return this.constructor(e,n)}function z(e,t,n){var i,s,a,u;t==document&&(t=document.documentElement),o.__&&o.__(e,t),s=(i="function"==typeof n)?null:n&&n.__k||t.__k,a=[],u=[],A(t,e=(!i&&n||t).__k=k(x,null,[e]),s||v,v,t.namespaceURI,!i&&n?[n]:s?null:t.firstChild?r.call(t.childNodes):null,a,!i&&n?n:s?s.__e:t.firstChild,i,u),W(a,e,u)}function V(e,t){z(e,t,V)}function B(e,t,n){var o,i,s,a,u=w({},e.props);for(s in e.type&&e.type.defaultProps&&(a=e.type.defaultProps),t)"key"==s?o=t[s]:"ref"==s?i=t[s]:u[s]=void 0===t[s]&&void 0!==a?a[s]:t[s];return arguments.length>2&&(u.children=arguments.length>3?r.call(arguments,2):n),S(e.type,u,o||e.key,i||e.ref,null)}function J(e,t){var n={__c:t="__cC"+d++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.forEach((function(e){e.__e=!0,P(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}r=y.slice,o={__e:function(e,t,n,r){for(var o,i,s;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),s=o.__d),s)return o.__E=o}catch(t){e=t}throw e}},i=0,s=function(e){return null!=e&&null==e.constructor},E.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=w({},this.state),"function"==typeof e&&(e=e(w({},n),this.props)),e&&w(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),P(this))},E.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),P(this))},E.prototype.render=x,a=[],l="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,c=function(e,t){return e.__v.__b-t.__v.__b},F.__r=0,_=/(PointerCapture)$|Capture$/i,f=0,p=U(!1),h=U(!0),d=0}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r={};(()=>{n.d(r,{zj:()=>w.zj,SD:()=>w.SD,V6:()=>w.V6,$K:()=>w.$K,vT:()=>w.vT,jb:()=>w.jb,yT:()=>w.yT,M_:()=>w.M_,hb:()=>w.hb,vJ:()=>w.vJ,ip:()=>w.ip,Nf:()=>w.Nf,Kr:()=>w.Kr,li:()=>w.li,J0:()=>w.J0,FH:()=>w.FH,v4:()=>w.v4});var e,t=n(622);null!=(e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0)&&e.__PREACT_DEVTOOLS__&&e.__PREACT_DEVTOOLS__.attachPreact("10.25.4",t.fF,{Fragment:t.FK,Component:t.uA});var o={};function i(e){return e.type===t.FK?"Fragment":"function"==typeof e.type?e.type.displayName||e.type.name:"string"==typeof e.type?e.type:"#text"}var s=[],a=[];function u(){return s.length>0?s[s.length-1]:null}var l=!0;function c(e){return"function"==typeof e.type&&e.type!=t.FK}function _(e){for(var t=[e],n=e;null!=n.__o;)t.push(n.__o),n=n.__o;return t.reduce((function(e,t){e+="  in "+i(t);var n=t.__source;return n?e+=" (at "+n.fileName+":"+n.lineNumber+")":l&&console.warn("Add @babel/plugin-transform-react-jsx-source to get a more detailed component stack. Note that you should not add it to production builds of your App for bundle size reasons."),l=!1,e+"\n"}),"")}var f="function"==typeof WeakMap;function p(e){var t=[];return e.__k?(e.__k.forEach((function(e){e&&"function"==typeof e.type?t.push.apply(t,p(e)):e&&"string"==typeof e.type&&t.push(e.type)})),t):t}function h(e){return e?"function"==typeof e.type?null==e.__?null!=e.__e&&null!=e.__e.parentNode?e.__e.parentNode.localName:"":h(e.__):e.type:""}var d=t.uA.prototype.setState;function v(e){return"table"===e||"tfoot"===e||"tbody"===e||"thead"===e||"td"===e||"tr"===e||"th"===e}t.uA.prototype.setState=function(e,t){return null==this.__v&&null==this.state&&console.warn('Calling "this.setState" inside the constructor of a component is a no-op and might be a bug in your application. Instead, set "this.state = {}" directly.\n\n'+_(u())),d.call(this,e,t)};var y=/^(address|article|aside|blockquote|details|div|dl|fieldset|figcaption|figure|footer|form|h1|h2|h3|h4|h5|h6|header|hgroup|hr|main|menu|nav|ol|p|pre|search|section|table|ul)$/,m=t.uA.prototype.forceUpdate;function g(e){var t=e.props,n=i(e),r="";for(var o in t)if(t.hasOwnProperty(o)&&"children"!==o){var s=t[o];"function"==typeof s&&(s="function "+(s.displayName||s.name)+"() {}"),s=Object(s)!==s||s.toString?s+"":Object.prototype.toString.call(s),r+=" "+o+"="+JSON.stringify(s)}var a=t.children;return"<"+n+r+(a&&a.length?">..</"+n+">":" />")}t.uA.prototype.forceUpdate=function(e){return null==this.__v?console.warn('Calling "this.forceUpdate" inside the constructor of a component is a no-op and might be a bug in your application.\n\n'+_(u())):null==this.__P&&console.warn('Can\'t call "this.forceUpdate" on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in the componentWillUnmount method.\n\n'+_(this.__v)),m.call(this,e)},t.fF.__m=function(e,t){var n=e.type,r=t.map((function(e){return e&&e.localName})).filter(Boolean);console.error('Expected a DOM node of type "'+n+'" but found "'+r.join(", ")+"\" as available DOM-node(s), this is caused by the SSR'd HTML containing different DOM-nodes compared to the hydrated one.\n\n"+_(e))},function(){!function(){var e=t.fF.__b,n=t.fF.diffed,r=t.fF.__,o=t.fF.vnode,i=t.fF.__r;t.fF.diffed=function(e){c(e)&&a.pop(),s.pop(),n&&n(e)},t.fF.__b=function(t){c(t)&&s.push(t),e&&e(t)},t.fF.__=function(e,t){a=[],r&&r(e,t)},t.fF.vnode=function(e){e.__o=a.length>0?a[a.length-1]:null,o&&o(e)},t.fF.__r=function(e){c(e)&&a.push(e),i&&i(e)}}();var e=!1,n=t.fF.__b,r=t.fF.diffed,u=t.fF.vnode,l=t.fF.__r,d=t.fF.__e,m=t.fF.__,w=t.fF.__h,b=f?{useEffect:new WeakMap,useLayoutEffect:new WeakMap,lazyPropTypes:new WeakMap}:null,k=[];t.fF.__e=function(e,t,n,r){if(t&&t.__c&&"function"==typeof e.then){var o=e;e=new Error("Missing Suspense. The throwing component was: "+i(t));for(var s=t;s;s=s.__)if(s.__c&&s.__c.__c){e=o;break}if(e instanceof Error)throw e}try{(r=r||{}).componentStack=_(t),d(e,t,n,r),"function"!=typeof e.then&&setTimeout((function(){throw e}))}catch(e){throw e}},t.fF.__=function(e,t){if(!t)throw new Error("Undefined parent passed to render(), this is the second argument.\nCheck if the element is available in the DOM/has the correct id.");var n;switch(t.nodeType){case 1:case 11:case 9:n=!0;break;default:n=!1}if(!n){var r=i(e);throw new Error("Expected a valid HTML node as a second argument to render.\tReceived "+t+" instead: render(<"+r+" />, "+t+");")}m&&m(e,t)},t.fF.__b=function(t){var r=t.type;if(e=!0,void 0===r)throw new Error("Undefined component passed to createElement()\n\nYou likely forgot to export your component or might have mixed up default and named imports"+g(t)+"\n\n"+_(t));if(null!=r&&"object"==typeof r){if(void 0!==r.__k&&void 0!==r.__e)throw new Error("Invalid type passed to createElement(): "+r+"\n\nDid you accidentally pass a JSX literal as JSX twice?\n\n  let My"+i(t)+" = "+g(r)+";\n  let vnode = <My"+i(t)+" />;\n\nThis usually happens when you export a JSX literal and not the component.\n\n"+_(t));throw new Error("Invalid type passed to createElement(): "+(Array.isArray(r)?"array":r))}if(void 0!==t.ref&&"function"!=typeof t.ref&&"object"!=typeof t.ref&&!("$$typeof"in t))throw new Error('Component\'s "ref" property should be a function, or an object created by createRef(), but got ['+typeof t.ref+"] instead\n"+g(t)+"\n\n"+_(t));if("string"==typeof t.type)for(var s in t.props)if("o"===s[0]&&"n"===s[1]&&"function"!=typeof t.props[s]&&null!=t.props[s])throw new Error("Component's \""+s+'" property should be a function, but got ['+typeof t.props[s]+"] instead\n"+g(t)+"\n\n"+_(t));if("function"==typeof t.type&&t.type.propTypes){if("Lazy"===t.type.displayName&&b&&!b.lazyPropTypes.has(t.type)){var a="PropTypes are not supported on lazy(). Use propTypes on the wrapped component itself. ";try{var u=t.type();b.lazyPropTypes.set(t.type,!0),console.warn(a+"Component wrapped in lazy() is "+i(u))}catch(e){console.warn(a+"We will log the wrapped component's name once it is loaded.")}}var l=t.props;t.type.__f&&delete(l=function(e,t){for(var n in t)e[n]=t[n];return e}({},l)).ref,function(e,t,n,r,i){Object.keys(e).forEach((function(n){var s;try{s=e[n](t,n,r,"prop",null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(e){s=e}s&&!(s.message in o)&&(o[s.message]=!0,console.error("Failed prop type: "+s.message+(i&&"\n"+i()||"")))}))}(t.type.propTypes,l,0,i(t),(function(){return _(t)}))}n&&n(t)};var S,x=0;t.fF.__r=function(t){l&&l(t),e=!0;var n=t.__c;if(n===S?x++:x=1,x>=25)throw new Error("Too many re-renders. This is limited to prevent an infinite loop which may lock up your browser. The component causing this is: "+i(t));S=n},t.fF.__h=function(t,n,r){if(!t||!e)throw new Error("Hook can only be invoked from render methods.");w&&w(t,n,r)};var E=function(e,t){return{get:function(){var n="get"+e+t;k&&k.indexOf(n)<0&&(k.push(n),console.warn("getting vnode."+e+" is deprecated, "+t))},set:function(){var n="set"+e+t;k&&k.indexOf(n)<0&&(k.push(n),console.warn("setting vnode."+e+" is not allowed, "+t))}}},O={nodeName:E("nodeName","use vnode.type"),attributes:E("attributes","use vnode.props"),children:E("children","use vnode.props.children")},T=Object.create({},O);t.fF.vnode=function(e){var t=e.props;if(null!==e.type&&null!=t&&("__source"in t||"__self"in t)){var n=e.props={};for(var r in t){var o=t[r];"__source"===r?e.__source=o:"__self"===r?e.__self=o:n[r]=o}}e.__proto__=T,u&&u(e)},t.fF.diffed=function(t){var n,o=t.type,s=t.__;if(t.__k&&t.__k.forEach((function(e){if("object"==typeof e&&e&&void 0===e.type){var n=Object.keys(e).join(",");throw new Error("Objects are not valid as a child. Encountered an object with the keys {"+n+"}.\n\n"+_(t))}})),t.__c===S&&(x=0),"string"==typeof o&&(v(o)||"p"===o||"a"===o||"button"===o)){var a=h(s);if(""!==a&&v(o))"table"===o&&"td"!==a&&v(a)?(console.log(a,s.__e),console.error("Improper nesting of table. Your <table> should not have a table-node parent."+g(t)+"\n\n"+_(t))):"thead"!==o&&"tfoot"!==o&&"tbody"!==o||"table"===a?"tr"===o&&"thead"!==a&&"tfoot"!==a&&"tbody"!==a?console.error("Improper nesting of table. Your <tr> should have a <thead/tbody/tfoot> parent."+g(t)+"\n\n"+_(t)):"td"===o&&"tr"!==a?console.error("Improper nesting of table. Your <td> should have a <tr> parent."+g(t)+"\n\n"+_(t)):"th"===o&&"tr"!==a&&console.error("Improper nesting of table. Your <th> should have a <tr>."+g(t)+"\n\n"+_(t)):console.error("Improper nesting of table. Your <thead/tbody/tfoot> should have a <table> parent."+g(t)+"\n\n"+_(t));else if("p"===o){var u=p(t).filter((function(e){return y.test(e)}));u.length&&console.error("Improper nesting of paragraph. Your <p> should not have "+u.join(", ")+" as child-elements."+g(t)+"\n\n"+_(t))}else"a"!==o&&"button"!==o||-1!==p(t).indexOf(o)&&console.error("Improper nesting of interactive content. Your <"+o+"> should not have other "+("a"===o?"anchor":"button")+" tags as child-elements."+g(t)+"\n\n"+_(t))}if(e=!1,r&&r(t),null!=t.__k)for(var l=[],c=0;c<t.__k.length;c++){var f=t.__k[c];if(f&&null!=f.key){var d=f.key;if(-1!==l.indexOf(d)){console.error('Following component has two or more children with the same key attribute: "'+d+'". This may cause glitches and misbehavior in rendering process. Component: \n\n'+g(t)+"\n\n"+_(t));break}l.push(d)}}if(null!=t.__c&&null!=t.__c.__H){var m=t.__c.__H.__;if(m)for(var w=0;w<m.length;w+=1){var b=m[w];if(b.__H)for(var k=0;k<b.__H.length;k++)if((n=b.__H[k])!=n){var E=i(t);console.warn("Invalid argument passed to hook. Hooks should not be called with NaN in the dependency array. Hook index "+w+" in component "+E+" was called with NaN.")}}}}}();var w=n(380)})();var o=r.zj,i=r.SD,s=r.V6,a=r.$K,u=r.vT,l=r.jb,c=r.yT,_=r.M_,f=r.hb,p=r.vJ,h=r.ip,d=r.Nf,v=r.Kr,y=r.li,m=r.J0,g=r.FH,w=r.v4;export{o as getConfig,i as getContext,s as getElement,a as getServerContext,u as getServerState,l as privateApis,c as splitTask,_ as store,f as useCallback,p as useEffect,h as useInit,d as useLayoutEffect,v as useMemo,y as useRef,m as useState,g as useWatch,w as withScope};