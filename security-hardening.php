<?php

/**
 * WordPress Security Hardening Script
 * This script implements comprehensive security measures to protect against attacks
 * 
 * IMPORTANT: Run this script with caution and backup your site first!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

class WordPressSecurity
{

    private $log_file;
    private $malicious_patterns = [
        'eval(',
        'base64_decode(',
        'gzinflate(',
        'str_rot13(',
        'gzuncompress(',
        'system(',
        'exec(',
        'shell_exec(',
        'passthru(',
        'file_get_contents(',
        'curl_exec(',
        'fopen(',
        'fwrite(',
        'file_put_contents('
    ];

    public function __construct()
    {
        $this->log_file = ABSPATH . 'security-scan.log';
        $this->log("=== WordPress Security Hardening Started ===");
    }

    /**
     * Main security hardening function
     */
    public function hardenSecurity()
    {
        echo "Starting WordPress Security Hardening...\n";

        // 1. <PERSON>an for malicious files
        $this->scanForMaliciousFiles();

        // 2. Set proper file permissions
        $this->setFilePermissions();

        // 3. Secure wp-config.php
        $this->secureWpConfig();

        // 4. Create/update .htaccess security rules
        $this->updateHtaccess();

        // 5. Remove suspicious uploads
        $this->cleanUploadsDirectory();

        // 6. Update WordPress core and plugins
        $this->updateWordPressAndPlugins();

        // 7. Strengthen authentication security
        $this->strengthenAuthentication();

        // 8. Configure Web Application Firewall
        $this->configureWAF();

        // 9. Secure file upload functionality
        $this->secureFileUploads();

        // 10. Database security review
        $this->reviewDatabaseSecurity();

        // 11. Implement security monitoring
        $this->implementSecurityMonitoring();

        // 12. Generate security report
        $this->generateSecurityReport();

        echo "Security hardening completed. Check security-scan.log for details.\n";
    }

    /**
     * Update WordPress core and plugins
     */
    private function updateWordPressAndPlugins()
    {
        $this->log("Checking for WordPress and plugin updates...");

        // Check WordPress core version
        $wp_version = get_bloginfo('version');
        $this->log("Current WordPress version: $wp_version");

        // Get available updates
        wp_update_plugins();
        wp_update_themes();

        $plugin_updates = get_site_transient('update_plugins');
        $theme_updates = get_site_transient('update_themes');

        if (!empty($plugin_updates->response)) {
            $this->log("Plugin updates available:");
            foreach ($plugin_updates->response as $plugin => $update) {
                $this->log("- $plugin: " . $update->new_version);
            }
        } else {
            $this->log("All plugins are up to date");
        }

        if (!empty($theme_updates->response)) {
            $this->log("Theme updates available:");
            foreach ($theme_updates->response as $theme => $update) {
                $this->log("- $theme: " . $update['new_version']);
            }
        } else {
            $this->log("All themes are up to date");
        }
    }

    /**
     * Strengthen authentication security
     */
    private function strengthenAuthentication()
    {
        $this->log("Strengthening authentication security...");

        // Add security constants to wp-config.php
        $this->addAuthenticationConstants();

        // Create login security functions
        $this->createLoginSecurityFunctions();

        // Check for weak admin users
        $this->checkAdminUsers();

        $this->log("Authentication security measures implemented");
    }

    /**
     * Add authentication security constants
     */
    private function addAuthenticationConstants()
    {
        $wp_config_path = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_path)) {
            $this->log("wp-config.php not found!");
            return;
        }

        $content = file_get_contents($wp_config_path);

        // Add login security constants
        $auth_constants = [
            "define('WP_FAIL2BAN_BLOCKED_USERS', array('admin', 'administrator', 'root'));",
            "define('LIMIT_LOGIN_ATTEMPTS', true);",
            "define('WP_LOGIN_ATTEMPTS', 3);",
            "define('WP_LOGIN_LOCKOUT_TIME', 1800);", // 30 minutes
        ];

        $modified = false;
        foreach ($auth_constants as $constant) {
            if (strpos($content, $constant) === false) {
                $content = str_replace("<?php", "<?php\n" . $constant, $content);
                $modified = true;
                $this->log("Added authentication constant: $constant");
            }
        }

        if ($modified) {
            file_put_contents($wp_config_path, $content);
            $this->log("wp-config.php updated with authentication constants");
        }
    }

    /**
     * Create login security functions
     */
    private function createLoginSecurityFunctions()
    {
        $functions_file = ABSPATH . 'wp-content/themes/' . get_template() . '/functions.php';

        if (!file_exists($functions_file)) {
            $this->log("Theme functions.php not found, creating security plugin file");
            $functions_file = ABSPATH . 'wp-content/mu-plugins/security-functions.php';

            // Create mu-plugins directory if it doesn't exist
            $mu_plugins_dir = dirname($functions_file);
            if (!is_dir($mu_plugins_dir)) {
                mkdir($mu_plugins_dir, 0755, true);
            }
        }

        $security_functions = "
// Security Functions - Added by Security Hardening Script
if (!function_exists('limit_login_attempts')) {
    function limit_login_attempts() {
        \$attempts = get_option('failed_login_attempts', array());
        \$ip = \$_SERVER['REMOTE_ADDR'];
        \$current_time = time();

        // Clean old attempts (older than 1 hour)
        foreach (\$attempts as \$attempt_ip => \$data) {
            if (\$current_time - \$data['time'] > 3600) {
                unset(\$attempts[\$attempt_ip]);
            }
        }

        // Check if IP is blocked
        if (isset(\$attempts[\$ip]) && \$attempts[\$ip]['count'] >= 3) {
            if (\$current_time - \$attempts[\$ip]['time'] < 1800) { // 30 minutes
                wp_die('Too many failed login attempts. Please try again later.');
            } else {
                unset(\$attempts[\$ip]); // Reset after lockout period
            }
        }

        update_option('failed_login_attempts', \$attempts);
    }
}

// Hook to track failed logins
add_action('wp_login_failed', function(\$username) {
    \$attempts = get_option('failed_login_attempts', array());
    \$ip = \$_SERVER['REMOTE_ADDR'];

    if (!isset(\$attempts[\$ip])) {
        \$attempts[\$ip] = array('count' => 0, 'time' => time());
    }

    \$attempts[\$ip]['count']++;
    \$attempts[\$ip]['time'] = time();

    update_option('failed_login_attempts', \$attempts);

    // Log the failed attempt
    error_log('Failed login attempt from IP: ' . \$ip . ' for username: ' . \$username);
});

// Hook to check login attempts
add_action('wp_authenticate', 'limit_login_attempts');

// Hide login errors
add_filter('login_errors', function(\$error) {
    return 'Invalid login credentials.';
});

// Remove WordPress version from head
remove_action('wp_head', 'wp_generator');

// Disable XML-RPC
add_filter('xmlrpc_enabled', '__return_false');

";

        if (file_exists($functions_file)) {
            $current_content = file_get_contents($functions_file);
            if (strpos($current_content, '// Security Functions - Added by Security Hardening Script') === false) {
                file_put_contents($functions_file, $current_content . $security_functions);
                $this->log("Security functions added to: $functions_file");
            }
        } else {
            file_put_contents($functions_file, "<?php\n" . $security_functions);
            $this->log("Security functions file created: $functions_file");
        }
    }

    /**
     * Check for weak admin users
     */
    private function checkAdminUsers()
    {
        $this->log("Checking for weak admin users...");

        // Common weak usernames to check for
        $weak_usernames = ['admin', 'administrator', 'root', 'user', 'test', 'demo'];

        foreach ($weak_usernames as $username) {
            $user = get_user_by('login', $username);
            if ($user && user_can($user, 'administrator')) {
                $this->log("WARNING: Weak admin username found: $username (ID: {$user->ID})");
                $this->log("RECOMMENDATION: Change this username or remove this user");
            }
        }

        // Check for users with weak passwords (this is limited without actual password access)
        $admin_users = get_users(array('role' => 'administrator'));
        foreach ($admin_users as $user) {
            $this->log("Admin user found: {$user->user_login} (ID: {$user->ID}, Email: {$user->user_email})");
        }
    }

    /**
     * Configure Web Application Firewall
     */
    private function configureWAF()
    {
        $this->log("Configuring Web Application Firewall...");

        // Check if Wordfence is installed
        if (!is_dir(ABSPATH . 'wp-content/plugins/wordfence')) {
            $this->log("Wordfence plugin not found. Please install Wordfence Security plugin.");
            return;
        }

        // Configure Wordfence settings
        $this->configureWordfenceSettings();

        // Ensure WAF is properly configured
        $this->ensureWAFConfiguration();

        // Configure additional firewall rules
        $this->configureFirewallRules();

        $this->log("Web Application Firewall configuration completed");
    }

    /**
     * Configure Wordfence settings
     */
    private function configureWordfenceSettings()
    {
        $this->log("Configuring Wordfence security settings...");

        // Optimal Wordfence settings for security
        $wordfence_settings = [
            // Enable real-time malware scanning
            'scansEnabled_malware' => 1,
            'scansEnabled_fileContents' => 1,
            'scansEnabled_core' => 1,
            'scansEnabled_plugins' => 1,
            'scansEnabled_themes' => 1,
            'scansEnabled_suspectedFiles' => 1,
            'scansEnabled_suspiciousOptions' => 1,
            'scansEnabled_passwds' => 1,
            'scansEnabled_wafStatus' => 1,

            // Enable login security
            'loginSec_lockInvalidUsers' => 1,
            'loginSec_lockoutMins' => 30,
            'loginSec_maxFailures' => 3,
            'loginSec_strongPasswds' => 1,
            'loginSec_breachPasswds' => 1,

            // Enable brute force protection
            'loginSec_enableSeparateTwoFactor' => 1,
            'loginSec_maxForgotPasswd' => 3,

            // Enable rate limiting
            'maxGlobalRequests' => 240,
            'maxGlobalRequests_action' => 'throttle',
            'maxRequestsHumans' => 60,
            'maxRequestsHumans_action' => 'throttle',
            'maxRequestsCrawlers' => 30,
            'maxRequestsCrawlers_action' => 'throttle',

            // Enable 404 detection
            'max404Humans' => 20,
            'max404Humans_action' => 'throttle',
            'max404Crawlers' => 10,
            'max404Crawlers_action' => 'block',

            // Enable alerts
            'alertOn_block' => 1,
            'alertOn_loginLockout' => 1,
            'alertOn_breachLogin' => 1,
            'alertOn_scanIssues' => 1,

            // Enable scheduled scans
            'scheduledScansEnabled' => 1,
        ];

        // Apply settings if Wordfence functions are available
        if (function_exists('wfConfig')) {
            foreach ($wordfence_settings as $key => $value) {
                try {
                    wfConfig::set($key, $value);
                    $this->log("Set Wordfence option: $key = $value");
                } catch (Exception $e) {
                    $this->log("Failed to set Wordfence option $key: " . $e->getMessage());
                }
            }
        } else {
            $this->log("Wordfence configuration functions not available. Settings will be applied when plugin is active.");
        }
    }

    /**
     * Ensure WAF configuration
     */
    private function ensureWAFConfiguration()
    {
        $this->log("Ensuring WAF configuration...");

        // Check if wordfence-waf.php exists in root
        $waf_file = ABSPATH . 'wordfence-waf.php';
        if (!file_exists($waf_file)) {
            $this->log("Creating wordfence-waf.php file...");
            $waf_content = '<?php
// Before removing this file, please verify the PHP ini setting `auto_prepend_file` does not point to this.

if (file_exists(__DIR__.\'/wp-content/plugins/wordfence/waf/bootstrap.php\')) {
    define("WFWAF_LOG_PATH", __DIR__.\'/wp-content/wflogs/\');
    include_once __DIR__.\'/wp-content/plugins/wordfence/waf/bootstrap.php\';
}';
            file_put_contents($waf_file, $waf_content);
            $this->log("wordfence-waf.php created");
        }

        // Ensure wflogs directory exists with proper permissions
        $wflogs_dir = ABSPATH . 'wp-content/wflogs';
        if (!is_dir($wflogs_dir)) {
            mkdir($wflogs_dir, 0755, true);
            $this->log("Created wflogs directory");
        }

        // Set proper permissions
        chmod($wflogs_dir, 0755);

        // Create .htaccess to protect logs
        $wflogs_htaccess = $wflogs_dir . '/.htaccess';
        if (!file_exists($wflogs_htaccess)) {
            $htaccess_content = "deny from all\n";
            file_put_contents($wflogs_htaccess, $htaccess_content);
            $this->log("Created .htaccess protection for wflogs directory");
        }
    }

    /**
     * Configure additional firewall rules
     */
    private function configureFirewallRules()
    {
        $this->log("Configuring additional firewall rules...");

        // Add advanced security rules to .htaccess
        $htaccess_path = ABSPATH . '.htaccess';
        $advanced_rules = "
# Advanced Firewall Rules - Added by Security Hardening Script
<IfModule mod_rewrite.c>
RewriteEngine On

# Block suspicious user agents
RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|\"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
RewriteRule .* - [F,L]

# Block suspicious requests
RewriteCond %{QUERY_STRING} (eval\(|base64_decode|gzinflate|system\(|exec\(|shell_exec) [NC,OR]
RewriteCond %{QUERY_STRING} (GLOBALS|REQUEST|_GET|_POST|_COOKIE|_FILES|_SESSION|_ENV|_SERVER) [NC,OR]
RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (<|%3C).*iframe.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (<|%3C).*object.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (<|%3C).*embed.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\\x00|\\x04|\\x08|\\x0d|\\x1b|\\x20|\\x3c|\\x3e|\\x7f) [NC]
RewriteRule .* - [F,L]

# Block file injection attacks
RewriteCond %{REQUEST_METHOD} GET
RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=http:// [OR]
RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=(\.\.//?)+ [OR]
RewriteCond %{QUERY_STRING} [a-zA-Z0-9_]=/([a-z0-9_.]//?)+ [NC]
RewriteRule .* - [F,L]

# Block SQL injection attempts
RewriteCond %{QUERY_STRING} (union.*select|select.*union|union.*all|all.*union) [NC,OR]
RewriteCond %{QUERY_STRING} (concat.*\(|group.*by.*\(|having.*\(|select.*\() [NC,OR]
RewriteCond %{QUERY_STRING} (insert.*into|update.*set|delete.*from) [NC]
RewriteRule .* - [F,L]
</IfModule>

# Block access to sensitive files
<FilesMatch \"(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$\">
Order allow,deny
Deny from all
Satisfy All
</FilesMatch>

";

        if (file_exists($htaccess_path)) {
            $current_content = file_get_contents($htaccess_path);
            if (strpos($current_content, '# Advanced Firewall Rules - Added by Security Hardening Script') === false) {
                file_put_contents($htaccess_path, $advanced_rules . $current_content);
                $this->log("Advanced firewall rules added to .htaccess");
            }
        } else {
            file_put_contents($htaccess_path, $advanced_rules);
            $this->log("Created .htaccess with advanced firewall rules");
        }
    }

    /**
     * Secure file upload functionality
     */
    private function secureFileUploads()
    {
        $this->log("Securing file upload functionality...");

        // Secure WordPress uploads directory
        $this->secureUploadsDirectory();

        // Add file upload restrictions
        $this->addUploadRestrictions();

        // Create upload security functions
        $this->createUploadSecurityFunctions();

        // Secure contact form uploads
        $this->secureContactFormUploads();

        $this->log("File upload security measures implemented");
    }

    /**
     * Secure uploads directory
     */
    private function secureUploadsDirectory()
    {
        $this->log("Securing uploads directory...");

        $uploads_dir = ABSPATH . 'wp-content/uploads';

        // Create .htaccess in uploads directory to prevent PHP execution
        $uploads_htaccess = $uploads_dir . '/.htaccess';
        $htaccess_content = "# Prevent PHP execution in uploads directory
<Files *.php>
Order Deny,Allow
Deny from all
</Files>
<Files *.phtml>
Order Deny,Allow
Deny from all
</Files>
<Files *.php5>
Order Deny,Allow
Deny from all
</Files>
<Files *.php7>
Order Deny,Allow
Deny from all
</Files>
<Files *.phar>
Order Deny,Allow
Deny from all
</Files>

# Block suspicious file types
<FilesMatch \"\.(bat|exe|cmd|sh|php|pl|cgi|386|dll|com|torrent|js|app|jar|pif|vb|vbscript|wsf|asp|cer|csr|jsp|drv|sys|ade|adp|bas|chm|cpl|crt|csh|fxp|hlp|hta|inf|ins|isp|jse|htaccess|htpasswd|ksh|lnk|mdb|mde|mdt|mdw|msc|msi|msp|mst|ops|pcd|prg|reg|scr|sct|shb|shs|url|vbe|vbs|wsc|wsf|wsh)$\">
Order allow,deny
Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes
";

        if (!file_exists($uploads_htaccess)) {
            file_put_contents($uploads_htaccess, $htaccess_content);
            $this->log("Created .htaccess protection for uploads directory");
        } else {
            $current_content = file_get_contents($uploads_htaccess);
            if (strpos($current_content, '# Prevent PHP execution in uploads directory') === false) {
                file_put_contents($uploads_htaccess, $htaccess_content . "\n" . $current_content);
                $this->log("Updated .htaccess protection for uploads directory");
            }
        }

        // Set proper permissions for uploads directory
        chmod($uploads_dir, 0755);

        // Recursively set permissions for subdirectories
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($uploads_dir)
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                chmod($item->getPathname(), 0755);
            } elseif ($item->isFile()) {
                chmod($item->getPathname(), 0644);
            }
        }

        $this->log("Set proper permissions for uploads directory");
    }

    /**
     * Add upload restrictions
     */
    private function addUploadRestrictions()
    {
        $this->log("Adding upload restrictions...");

        // Add upload restrictions to wp-config.php
        $wp_config_path = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_path)) {
            $this->log("wp-config.php not found!");
            return;
        }

        $content = file_get_contents($wp_config_path);

        // Add upload security constants
        $upload_constants = [
            "define('ALLOW_UNFILTERED_UPLOADS', false);",
            "define('WP_MAX_UPLOAD_SIZE', '10M');",
            "ini_set('upload_max_filesize', '10M');",
            "ini_set('post_max_size', '10M');",
        ];

        $modified = false;
        foreach ($upload_constants as $constant) {
            if (strpos($content, $constant) === false) {
                $content = str_replace("<?php", "<?php\n" . $constant, $content);
                $modified = true;
                $this->log("Added upload restriction: $constant");
            }
        }

        if ($modified) {
            file_put_contents($wp_config_path, $content);
            $this->log("wp-config.php updated with upload restrictions");
        }
    }

    /**
     * Create upload security functions
     */
    private function createUploadSecurityFunctions()
    {
        $this->log("Creating upload security functions...");

        $functions_file = ABSPATH . 'wp-content/mu-plugins/upload-security.php';

        // Create mu-plugins directory if it doesn't exist
        $mu_plugins_dir = dirname($functions_file);
        if (!is_dir($mu_plugins_dir)) {
            mkdir($mu_plugins_dir, 0755, true);
        }

        $security_functions = '<?php
// Upload Security Functions - Added by Security Hardening Script

// Restrict file upload types
function restrict_upload_mimes($mimes) {
    // Remove potentially dangerous file types
    unset($mimes[\'exe\']);
    unset($mimes[\'bat\']);
    unset($mimes[\'cmd\']);
    unset($mimes[\'com\']);
    unset($mimes[\'pif\']);
    unset($mimes[\'scr\']);
    unset($mimes[\'vbs\']);
    unset($mimes[\'vbe\']);
    unset($mimes[\'jse\']);
    unset($mimes[\'ws\']);
    unset($mimes[\'wsf\']);
    unset($mimes[\'wsc\']);
    unset($mimes[\'wsh\']);
    unset($mimes[\'ps1\']);
    unset($mimes[\'ps1xml\']);
    unset($mimes[\'ps2\']);
    unset($mimes[\'ps2xml\']);
    unset($mimes[\'psc1\']);
    unset($mimes[\'psc2\']);
    unset($mimes[\'msh\']);
    unset($mimes[\'msh1\']);
    unset($mimes[\'msh2\']);
    unset($mimes[\'mshxml\']);
    unset($mimes[\'msh1xml\']);
    unset($mimes[\'msh2xml\']);

    // Only allow safe file types
    $allowed_mimes = array(
        \'jpg|jpeg|jpe\' => \'image/jpeg\',
        \'gif\' => \'image/gif\',
        \'png\' => \'image/png\',
        \'bmp\' => \'image/bmp\',
        \'tiff|tif\' => \'image/tiff\',
        \'ico\' => \'image/x-icon\',
        \'pdf\' => \'application/pdf\',
        \'doc\' => \'application/msword\',
        \'docx\' => \'application/vnd.openxmlformats-officedocument.wordprocessingml.document\',
        \'xls\' => \'application/vnd.ms-excel\',
        \'xlsx\' => \'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\',
        \'ppt\' => \'application/vnd.ms-powerpoint\',
        \'pptx\' => \'application/vnd.openxmlformats-officedocument.presentationml.presentation\',
        \'txt\' => \'text/plain\',
        \'csv\' => \'text/csv\',
        \'zip\' => \'application/zip\',
    );

    return $allowed_mimes;
}
add_filter(\'upload_mimes\', \'restrict_upload_mimes\');

// Check file content for malicious code
function check_file_content($file) {
    $filename = $file[\'name\'];
    $filetype = wp_check_filetype($filename);

    // Check for suspicious file extensions
    $dangerous_extensions = array(\'php\', \'phtml\', \'php3\', \'php4\', \'php5\', \'php7\', \'phar\', \'exe\', \'bat\', \'cmd\', \'com\', \'pif\', \'scr\', \'vbs\', \'js\', \'jar\', \'sh\', \'py\', \'pl\', \'cgi\');

    $file_extension = pathinfo($filename, PATHINFO_EXTENSION);
    if (in_array(strtolower($file_extension), $dangerous_extensions)) {
        $file[\'error\'] = \'File type not allowed for security reasons.\';
        return $file;
    }

    // Check file content for malicious patterns
    if (isset($file[\'tmp_name\']) && is_uploaded_file($file[\'tmp_name\'])) {
        $content = file_get_contents($file[\'tmp_name\']);

        // Patterns that indicate malicious content
        $malicious_patterns = array(
            \'eval(\',
            \'base64_decode(\',
            \'gzinflate(\',
            \'system(\',
            \'exec(\',
            \'shell_exec(\',
            \'passthru(\',
            \'<?php\',
            \'<script\',
            \'javascript:\',
            \'vbscript:\',
        );

        foreach ($malicious_patterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                $file[\'error\'] = \'File contains suspicious content and cannot be uploaded.\';
                return $file;
            }
        }
    }

    return $file;
}
add_filter(\'wp_handle_upload_prefilter\', \'check_file_content\');

// Limit file upload size
function limit_upload_size($file) {
    $max_size = 10 * 1024 * 1024; // 10MB

    if ($file[\'size\'] > $max_size) {
        $file[\'error\'] = \'File size exceeds maximum allowed size of 10MB.\';
    }

    return $file;
}
add_filter(\'wp_handle_upload_prefilter\', \'limit_upload_size\');

// Rename uploaded files to prevent direct access
function secure_upload_filename($filename) {
    $info = pathinfo($filename);
    $ext = empty($info[\'extension\']) ? \'\' : \'.\' . $info[\'extension\'];
    $name = basename($filename, $ext);

    // Generate secure filename
    $secure_name = sanitize_file_name($name) . \'_\' . wp_generate_password(8, false) . $ext;

    return $secure_name;
}
add_filter(\'sanitize_file_name\', \'secure_upload_filename\');
';

        if (!file_exists($functions_file)) {
            file_put_contents($functions_file, $security_functions);
            $this->log("Created upload security functions file: $functions_file");
        } else {
            $current_content = file_get_contents($functions_file);
            if (strpos($current_content, '// Upload Security Functions - Added by Security Hardening Script') === false) {
                file_put_contents($functions_file, $current_content . "\n" . $security_functions);
                $this->log("Updated upload security functions file: $functions_file");
            }
        }
    }

    /**
     * Secure contact form uploads
     */
    private function secureContactFormUploads()
    {
        $this->log("Securing contact form uploads...");

        // Check if Contact Form 7 is installed
        if (is_dir(ABSPATH . 'wp-content/plugins/contact-form-7')) {
            $this->log("Contact Form 7 detected - implementing additional security");

            // Create CF7 upload restrictions
            $cf7_security = '<?php
// Contact Form 7 Security - Added by Security Hardening Script

// Restrict CF7 file uploads
function secure_cf7_uploads($result, $tag) {
    if ($tag->type !== \'file\') {
        return $result;
    }

    $files = $_FILES[$tag->name];

    if (!is_array($files[\'name\'])) {
        $files = array(
            \'name\' => array($files[\'name\']),
            \'type\' => array($files[\'type\']),
            \'tmp_name\' => array($files[\'tmp_name\']),
            \'error\' => array($files[\'error\']),
            \'size\' => array($files[\'size\'])
        );
    }

    foreach ($files[\'name\'] as $key => $filename) {
        if (empty($filename)) continue;

        // Check file extension
        $allowed_extensions = array(\'jpg\', \'jpeg\', \'png\', \'gif\', \'pdf\', \'doc\', \'docx\', \'txt\');
        $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (!in_array($file_extension, $allowed_extensions)) {
            $result->invalidate($tag, \'File type not allowed.\');
            break;
        }

        // Check file size (5MB max)
        if ($files[\'size\'][$key] > 5 * 1024 * 1024) {
            $result->invalidate($tag, \'File size too large. Maximum 5MB allowed.\');
            break;
        }
    }

    return $result;
}
add_filter(\'wpcf7_validate_file\', \'secure_cf7_uploads\', 10, 2);
add_filter(\'wpcf7_validate_file*\', \'secure_cf7_uploads\', 10, 2);
';

            $cf7_security_file = ABSPATH . 'wp-content/mu-plugins/cf7-security.php';
            if (!file_exists($cf7_security_file)) {
                file_put_contents($cf7_security_file, $cf7_security);
                $this->log("Created Contact Form 7 security file");
            }
        }

        // Check if WPForms is installed
        if (is_dir(ABSPATH . 'wp-content/plugins/wpforms-lite') || is_dir(ABSPATH . 'wp-content/plugins/wpforms')) {
            $this->log("WPForms detected - implementing additional security");

            // WPForms already has good security, but add extra restrictions
            $wpforms_security = '<?php
// WPForms Security - Added by Security Hardening Script

// Additional WPForms upload restrictions
function secure_wpforms_uploads($errors, $form_data, $entry_id) {
    if (empty($_FILES)) {
        return $errors;
    }

    foreach ($_FILES as $field_id => $file) {
        if (empty($file[\'name\'])) continue;

        $filename = $file[\'name\'];
        $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        // Restrict to safe file types only
        $allowed_extensions = array(\'jpg\', \'jpeg\', \'png\', \'gif\', \'pdf\', \'doc\', \'docx\', \'txt\');

        if (!in_array($file_extension, $allowed_extensions)) {
            $errors[$field_id] = \'File type not allowed for security reasons.\';
        }

        // Check file size (10MB max)
        if ($file[\'size\'] > 10 * 1024 * 1024) {
            $errors[$field_id] = \'File size too large. Maximum 10MB allowed.\';
        }
    }

    return $errors;
}
add_filter(\'wpforms_process_before_save\', \'secure_wpforms_uploads\', 10, 3);
';

            $wpforms_security_file = ABSPATH . 'wp-content/mu-plugins/wpforms-security.php';
            if (!file_exists($wpforms_security_file)) {
                file_put_contents($wpforms_security_file, $wpforms_security);
                $this->log("Created WPForms security file");
            }
        }
    }

    /**
     * Review database security
     */
    private function reviewDatabaseSecurity()
    {
        $this->log("Reviewing database security...");

        // Check for suspicious users
        $this->checkSuspiciousUsers();

        // Check for suspicious orders (especially Desert Dream 2025)
        $this->checkSuspiciousOrders();

        // Check for malicious database entries
        $this->checkMaliciousEntries();

        // Check database permissions
        $this->checkDatabasePermissions();

        $this->log("Database security review completed");
    }

    /**
     * Check for suspicious users
     */
    private function checkSuspiciousUsers()
    {
        $this->log("Checking for suspicious users...");

        global $wpdb;

        // Check for users with suspicious usernames
        $suspicious_usernames = ['admin', 'administrator', 'root', 'test', 'demo', 'user', 'guest', 'hacker', 'wp-admin'];

        foreach ($suspicious_usernames as $username) {
            $user = get_user_by('login', $username);
            if ($user) {
                $this->log("SUSPICIOUS USER FOUND: {$user->user_login} (ID: {$user->ID}, Email: {$user->user_email}, Role: " . implode(', ', $user->roles) . ")");

                // Check if user has admin privileges
                if (user_can($user, 'administrator')) {
                    $this->log("WARNING: Suspicious user has administrator privileges!");
                }
            }
        }

        // Check for recently created admin users
        $recent_admins = get_users(array(
            'role' => 'administrator',
            'date_query' => array(
                array(
                    'after' => '30 days ago'
                )
            )
        ));

        if (!empty($recent_admins)) {
            $this->log("Recently created admin users (last 30 days):");
            foreach ($recent_admins as $admin) {
                $this->log("- {$admin->user_login} (ID: {$admin->ID}, Created: {$admin->user_registered})");
            }
        }

        // Check for users with no posts but admin access
        $admin_users = get_users(array('role' => 'administrator'));
        foreach ($admin_users as $admin) {
            $post_count = count_user_posts($admin->ID);
            if ($post_count == 0) {
                $this->log("Admin user with no posts: {$admin->user_login} (ID: {$admin->ID}) - Potentially suspicious");
            }
        }
    }

    /**
     * Check for suspicious orders
     */
    private function checkSuspiciousOrders()
    {
        $this->log("Checking for suspicious orders...");

        global $wpdb;

        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            $this->log("WooCommerce not active, skipping order checks");
            return;
        }

        // Check for failed orders in the last 30 days
        $failed_orders = $wpdb->get_results("
            SELECT p.ID, p.post_date, pm1.meta_value as order_total, pm2.meta_value as billing_email
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_order_total'
            LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_billing_email'
            WHERE p.post_type = 'shop_order'
            AND p.post_status = 'wc-failed'
            AND p.post_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
            ORDER BY p.post_date DESC
            LIMIT 50
        ");

        if (!empty($failed_orders)) {
            $this->log("Found " . count($failed_orders) . " failed orders in the last 30 days:");
            foreach ($failed_orders as $order) {
                $this->log("- Order #{$order->ID} on {$order->post_date} - Total: {$order->order_total} - Email: {$order->billing_email}");
            }
        }

        // Check specifically for Desert Dream 2025 orders
        $desert_dream_orders = $wpdb->get_results("
            SELECT p.ID, p.post_date, p.post_status, pm1.meta_value as order_total, pm2.meta_value as billing_email
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_order_total'
            LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_billing_email'
            LEFT JOIN {$wpdb->prefix}woocommerce_order_items oi ON p.ID = oi.order_id
            WHERE p.post_type = 'shop_order'
            AND oi.order_item_name LIKE '%Desert Dream 2025%'
            AND p.post_date > DATE_SUB(NOW(), INTERVAL 60 DAY)
            ORDER BY p.post_date DESC
        ");

        if (!empty($desert_dream_orders)) {
            $this->log("CRITICAL: Found " . count($desert_dream_orders) . " Desert Dream 2025 orders in the last 60 days:");
            foreach ($desert_dream_orders as $order) {
                $this->log("- Order #{$order->ID} on {$order->post_date} - Status: {$order->post_status} - Total: {$order->order_total} - Email: {$order->billing_email}");
            }
        }

        // Check for orders with suspicious patterns
        $suspicious_patterns = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        foreach ($suspicious_patterns as $pattern) {
            $suspicious_orders = $wpdb->get_results($wpdb->prepare("
                SELECT p.ID, p.post_date, p.post_status, pm1.meta_value as billing_email
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_billing_email'
                WHERE p.post_type = 'shop_order'
                AND pm1.meta_value LIKE %s
                AND p.post_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
            ", '%' . $pattern . '%'));

            if (!empty($suspicious_orders)) {
                $this->log("Orders with suspicious email pattern '$pattern':");
                foreach ($suspicious_orders as $order) {
                    $this->log("- Order #{$order->ID} on {$order->post_date} - Status: {$order->post_status} - Email: {$order->billing_email}");
                }
            }
        }
    }

    /**
     * Check for malicious database entries
     */
    private function checkMaliciousEntries()
    {
        $this->log("Checking for malicious database entries...");

        global $wpdb;

        // Check for suspicious options
        $suspicious_options = $wpdb->get_results("
            SELECT option_name, option_value
            FROM {$wpdb->options}
            WHERE option_value LIKE '%base64_decode%'
            OR option_value LIKE '%eval(%'
            OR option_value LIKE '%gzinflate%'
            OR option_value LIKE '%system(%'
            OR option_value LIKE '%exec(%'
            OR option_value LIKE '%shell_exec%'
            OR option_name LIKE '%hack%'
            OR option_name LIKE '%malware%'
            OR option_name LIKE '%backdoor%'
        ");

        if (!empty($suspicious_options)) {
            $this->log("CRITICAL: Found suspicious options in database:");
            foreach ($suspicious_options as $option) {
                $this->log("- Option: {$option->option_name} - Value: " . substr($option->option_value, 0, 100) . "...");
            }
        }

        // Check for suspicious posts/pages
        $suspicious_posts = $wpdb->get_results("
            SELECT ID, post_title, post_content, post_date
            FROM {$wpdb->posts}
            WHERE post_content LIKE '%base64_decode%'
            OR post_content LIKE '%eval(%'
            OR post_content LIKE '%<script%'
            OR post_content LIKE '%javascript:%'
            OR post_title LIKE '%hack%'
            OR post_title LIKE '%malware%'
            LIMIT 20
        ");

        if (!empty($suspicious_posts)) {
            $this->log("Found suspicious posts/pages:");
            foreach ($suspicious_posts as $post) {
                $this->log("- Post ID: {$post->ID} - Title: {$post->post_title} - Date: {$post->post_date}");
            }
        }

        // Check for suspicious comments
        $suspicious_comments = $wpdb->get_results("
            SELECT comment_ID, comment_author, comment_content, comment_date
            FROM {$wpdb->comments}
            WHERE comment_content LIKE '%<script%'
            OR comment_content LIKE '%javascript:%'
            OR comment_content LIKE '%base64_decode%'
            OR comment_author LIKE '%hack%'
            OR comment_author LIKE '%admin%'
            AND comment_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
            LIMIT 20
        ");

        if (!empty($suspicious_comments)) {
            $this->log("Found suspicious comments:");
            foreach ($suspicious_comments as $comment) {
                $this->log("- Comment ID: {$comment->comment_ID} - Author: {$comment->comment_author} - Date: {$comment->comment_date}");
            }
        }
    }

    /**
     * Check database permissions
     */
    private function checkDatabasePermissions()
    {
        $this->log("Checking database permissions...");

        global $wpdb;

        // Check current database user privileges
        $privileges = $wpdb->get_results("SHOW GRANTS FOR CURRENT_USER()");

        if (!empty($privileges)) {
            $this->log("Current database user privileges:");
            foreach ($privileges as $privilege) {
                $grant = array_values((array)$privilege)[0];
                $this->log("- " . $grant);

                // Check for dangerous privileges
                if (stripos($grant, 'ALL PRIVILEGES') !== false) {
                    $this->log("WARNING: Database user has ALL PRIVILEGES - consider restricting");
                }
                if (stripos($grant, 'FILE') !== false) {
                    $this->log("WARNING: Database user has FILE privilege - potential security risk");
                }
                if (stripos($grant, 'PROCESS') !== false) {
                    $this->log("WARNING: Database user has PROCESS privilege - consider if necessary");
                }
            }
        }

        // Check database version
        $db_version = $wpdb->get_var("SELECT VERSION()");
        $this->log("Database version: " . $db_version);

        // Check for database size (large databases might indicate spam/malicious content)
        $db_size = $wpdb->get_results("
            SELECT
                table_schema as 'Database',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
            FROM information_schema.tables
            WHERE table_schema = '" . DB_NAME . "'
            GROUP BY table_schema
        ");

        if (!empty($db_size)) {
            foreach ($db_size as $size) {
                $this->log("Database size: {$size->{'Size (MB)'}} MB");
                if ($size->{'Size (MB)'} > 500) {
                    $this->log("WARNING: Large database size - check for spam content");
                }
            }
        }
    }

    /**
     * Implement security monitoring
     */
    private function implementSecurityMonitoring()
    {
        $this->log("Implementing security monitoring...");

        // Create file integrity monitoring
        $this->createFileIntegrityMonitoring();

        // Set up login monitoring
        $this->setupLoginMonitoring();

        // Create security alerts system
        $this->createSecurityAlerts();

        // Set up automated scans
        $this->setupAutomatedScans();

        $this->log("Security monitoring implementation completed");
    }

    /**
     * Create file integrity monitoring
     */
    private function createFileIntegrityMonitoring()
    {
        $this->log("Creating file integrity monitoring...");

        $monitoring_file = ABSPATH . 'wp-content/mu-plugins/file-integrity-monitor.php';

        // Create mu-plugins directory if it doesn't exist
        $mu_plugins_dir = dirname($monitoring_file);
        if (!is_dir($mu_plugins_dir)) {
            mkdir($mu_plugins_dir, 0755, true);
        }

        $monitoring_code = '<?php
// File Integrity Monitoring - Added by Security Hardening Script

class FileIntegrityMonitor {
    private $monitored_files = [];
    private $hash_file;

    public function __construct() {
        $this->hash_file = WP_CONTENT_DIR . \'/file-hashes.json\';
        $this->monitored_files = [
            ABSPATH . \'wp-config.php\',
            ABSPATH . \'wp-admin/index.php\',
            ABSPATH . \'wp-includes/wp-db.php\',
            ABSPATH . \'wp-includes/functions.php\',
            ABSPATH . \'wp-login.php\',
            ABSPATH . \'index.php\',
            ABSPATH . \'.htaccess\'
        ];

        add_action(\'wp_loaded\', array($this, \'check_file_integrity\'));
        add_action(\'wp_loaded\', array($this, \'monitor_new_files\'));
    }

    public function generate_file_hashes() {
        $hashes = [];
        foreach ($this->monitored_files as $file) {
            if (file_exists($file)) {
                $hashes[$file] = md5_file($file);
            }
        }
        file_put_contents($this->hash_file, json_encode($hashes));
        return $hashes;
    }

    public function check_file_integrity() {
        if (!file_exists($this->hash_file)) {
            $this->generate_file_hashes();
            return;
        }

        $stored_hashes = json_decode(file_get_contents($this->hash_file), true);
        $current_hashes = [];

        foreach ($this->monitored_files as $file) {
            if (file_exists($file)) {
                $current_hash = md5_file($file);
                $current_hashes[$file] = $current_hash;

                if (isset($stored_hashes[$file]) && $stored_hashes[$file] !== $current_hash) {
                    $this->alert_file_change($file);
                }
            }
        }

        // Update hash file
        file_put_contents($this->hash_file, json_encode($current_hashes));
    }

    public function monitor_new_files() {
        $suspicious_locations = [
            ABSPATH,
            WP_CONTENT_DIR . \'/uploads/\'
        ];

        foreach ($suspicious_locations as $location) {
            $files = glob($location . \'*.php\');
            foreach ($files as $file) {
                if ($this->is_suspicious_file($file)) {
                    $this->alert_suspicious_file($file);
                }
            }
        }
    }

    private function is_suspicious_file($file) {
        $filename = basename($file);
        $suspicious_names = [\'wp-weo.php\', \'wp-wep.php\', \'wp-weq.php\', \'shell.php\', \'backdoor.php\', \'hack.php\'];

        if (in_array($filename, $suspicious_names)) {
            return true;
        }

        // Check file content for malicious patterns
        $content = file_get_contents($file);
        $malicious_patterns = [\'eval(\', \'base64_decode(\', \'gzinflate(\', \'system(\', \'exec(\'];

        foreach ($malicious_patterns as $pattern) {
            if (strpos($content, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    private function alert_file_change($file) {
        $message = "SECURITY ALERT: File integrity violation detected for: " . $file;
        error_log($message);

        // Send email alert if configured
        $admin_email = get_option(\'admin_email\');
        if ($admin_email) {
            wp_mail($admin_email, \'Security Alert - File Change Detected\', $message);
        }
    }

    private function alert_suspicious_file($file) {
        $message = "SECURITY ALERT: Suspicious file detected: " . $file;
        error_log($message);

        // Send email alert if configured
        $admin_email = get_option(\'admin_email\');
        if ($admin_email) {
            wp_mail($admin_email, \'Security Alert - Suspicious File Detected\', $message);
        }
    }
}

new FileIntegrityMonitor();
';

        if (!file_exists($monitoring_file)) {
            file_put_contents($monitoring_file, $monitoring_code);
            $this->log("Created file integrity monitoring system");
        }
    }

    /**
     * Set up login monitoring
     */
    private function setupLoginMonitoring()
    {
        $this->log("Setting up login monitoring...");

        $login_monitor_file = ABSPATH . 'wp-content/mu-plugins/login-monitor.php';

        $login_monitor_code = '<?php
// Login Monitoring - Added by Security Hardening Script

class LoginMonitor {
    public function __construct() {
        add_action(\'wp_login\', array($this, \'log_successful_login\'), 10, 2);
        add_action(\'wp_login_failed\', array($this, \'log_failed_login\'));
        add_action(\'wp_logout\', array($this, \'log_logout\'));
        add_action(\'admin_init\', array($this, \'check_suspicious_activity\'));
    }

    public function log_successful_login($user_login, $user) {
        $ip = $this->get_client_ip();
        $user_agent = $_SERVER[\'HTTP_USER_AGENT\'] ?? \'Unknown\';

        $message = sprintf(
            \'Successful login: User=%s, IP=%s, UserAgent=%s\',
            $user_login,
            $ip,
            $user_agent
        );

        error_log($message);

        // Check for suspicious login patterns
        if ($this->is_suspicious_login($user, $ip)) {
            $this->alert_suspicious_login($user_login, $ip);
        }
    }

    public function log_failed_login($username) {
        $ip = $this->get_client_ip();
        $user_agent = $_SERVER[\'HTTP_USER_AGENT\'] ?? \'Unknown\';

        $message = sprintf(
            \'Failed login attempt: Username=%s, IP=%s, UserAgent=%s\',
            $username,
            $ip,
            $user_agent
        );

        error_log($message);

        // Track failed attempts
        $this->track_failed_attempts($ip, $username);
    }

    public function log_logout() {
        $current_user = wp_get_current_user();
        $ip = $this->get_client_ip();

        $message = sprintf(
            \'User logout: User=%s, IP=%s\',
            $current_user->user_login,
            $ip
        );

        error_log($message);
    }

    private function is_suspicious_login($user, $ip) {
        // Check for admin login from unusual location
        if (user_can($user, \'administrator\')) {
            $last_login_ip = get_user_meta($user->ID, \'last_login_ip\', true);
            if ($last_login_ip && $last_login_ip !== $ip) {
                return true;
            }
        }

        // Check for login outside business hours (customize as needed)
        $hour = date(\'H\');
        if ($hour < 6 || $hour > 22) {
            return true;
        }

        return false;
    }

    private function track_failed_attempts($ip, $username) {
        $attempts = get_option(\'failed_login_tracking\', []);
        $key = $ip . \'_\' . $username;

        if (!isset($attempts[$key])) {
            $attempts[$key] = [\'count\' => 0, \'last_attempt\' => time()];
        }

        $attempts[$key][\'count\']++;
        $attempts[$key][\'last_attempt\'] = time();

        // Alert if too many failed attempts
        if ($attempts[$key][\'count\'] >= 5) {
            $this->alert_brute_force($ip, $username);
        }

        update_option(\'failed_login_tracking\', $attempts);
    }

    private function alert_suspicious_login($username, $ip) {
        $message = "SECURITY ALERT: Suspicious login detected for user: $username from IP: $ip";
        error_log($message);

        $admin_email = get_option(\'admin_email\');
        if ($admin_email) {
            wp_mail($admin_email, \'Security Alert - Suspicious Login\', $message);
        }
    }

    private function alert_brute_force($ip, $username) {
        $message = "SECURITY ALERT: Possible brute force attack detected. IP: $ip, Username: $username";
        error_log($message);

        $admin_email = get_option(\'admin_email\');
        if ($admin_email) {
            wp_mail($admin_email, \'Security Alert - Brute Force Attack\', $message);
        }
    }

    private function get_client_ip() {
        $ip_keys = [\'HTTP_CLIENT_IP\', \'HTTP_X_FORWARDED_FOR\', \'REMOTE_ADDR\'];
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(\',\', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        return $_SERVER[\'REMOTE_ADDR\'] ?? \'Unknown\';
    }

    public function check_suspicious_activity() {
        // Clean old tracking data (older than 24 hours)
        $attempts = get_option(\'failed_login_tracking\', []);
        $current_time = time();

        foreach ($attempts as $key => $data) {
            if ($current_time - $data[\'last_attempt\'] > 86400) { // 24 hours
                unset($attempts[$key]);
            }
        }

        update_option(\'failed_login_tracking\', $attempts);
    }
}

new LoginMonitor();
';

        if (!file_exists($login_monitor_file)) {
            file_put_contents($login_monitor_file, $login_monitor_code);
            $this->log("Created login monitoring system");
        }
    }

    /**
     * Create security alerts system
     */
    private function createSecurityAlerts()
    {
        $this->log("Creating security alerts system...");

        $alerts_file = ABSPATH . 'wp-content/mu-plugins/security-alerts.php';

        $alerts_code = '<?php
// Security Alerts System - Added by Security Hardening Script

class SecurityAlerts {
    public function __construct() {
        add_action(\'wp_loaded\', array($this, \'check_security_status\'));
        add_action(\'admin_notices\', array($this, \'show_security_notices\'));
    }

    public function check_security_status() {
        $alerts = [];

        // Check for outdated WordPress
        $wp_version = get_bloginfo(\'version\');
        $latest_version = $this->get_latest_wp_version();
        if (version_compare($wp_version, $latest_version, \'<\')) {
            $alerts[] = "WordPress is outdated. Current: $wp_version, Latest: $latest_version";
        }

        // Check for suspicious files
        if ($this->check_for_suspicious_files()) {
            $alerts[] = "Suspicious files detected in uploads directory";
        }

        // Check for failed login attempts
        $failed_attempts = get_option(\'failed_login_tracking\', []);
        if (count($failed_attempts) > 10) {
            $alerts[] = "High number of failed login attempts detected";
        }

        // Store alerts
        update_option(\'security_alerts\', $alerts);
    }

    public function show_security_notices() {
        $alerts = get_option(\'security_alerts\', []);

        foreach ($alerts as $alert) {
            echo \'<div class="notice notice-error"><p><strong>Security Alert:</strong> \' . esc_html($alert) . \'</p></div>\';
        }
    }

    private function get_latest_wp_version() {
        $response = wp_remote_get(\'https://api.wordpress.org/core/version-check/1.7/\');
        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            if (isset($data[\'offers\'][0][\'version\'])) {
                return $data[\'offers\'][0][\'version\'];
            }
        }
        return get_bloginfo(\'version\'); // Fallback to current version
    }

    private function check_for_suspicious_files() {
        $uploads_dir = wp_upload_dir();
        $suspicious_files = glob($uploads_dir[\'basedir\'] . \'/*.php\');
        return !empty($suspicious_files);
    }
}

new SecurityAlerts();
';

        if (!file_exists($alerts_file)) {
            file_put_contents($alerts_file, $alerts_code);
            $this->log("Created security alerts system");
        }
    }

    /**
     * Set up automated scans
     */
    private function setupAutomatedScans()
    {
        $this->log("Setting up automated scans...");

        $scanner_file = ABSPATH . 'wp-content/mu-plugins/automated-scanner.php';

        $scanner_code = '<?php
// Automated Security Scanner - Added by Security Hardening Script

class AutomatedScanner {
    public function __construct() {
        add_action(\'wp\', array($this, \'schedule_scans\'));
        add_action(\'security_daily_scan\', array($this, \'run_daily_scan\'));
        add_action(\'security_weekly_scan\', array($this, \'run_weekly_scan\'));
    }

    public function schedule_scans() {
        // Schedule daily scan
        if (!wp_next_scheduled(\'security_daily_scan\')) {
            wp_schedule_event(time(), \'daily\', \'security_daily_scan\');
        }

        // Schedule weekly scan
        if (!wp_next_scheduled(\'security_weekly_scan\')) {
            wp_schedule_event(time(), \'weekly\', \'security_weekly_scan\');
        }
    }

    public function run_daily_scan() {
        $this->log_scan("Starting daily security scan");

        // Quick file integrity check
        $this->quick_file_check();

        // Check for new admin users
        $this->check_new_admin_users();

        // Check for failed login attempts
        $this->check_failed_logins();

        $this->log_scan("Daily security scan completed");
    }

    public function run_weekly_scan() {
        $this->log_scan("Starting weekly security scan");

        // Full malware scan
        $this->full_malware_scan();

        // Database integrity check
        $this->database_integrity_check();

        // Plugin/theme update check
        $this->update_check();

        $this->log_scan("Weekly security scan completed");
    }

    private function quick_file_check() {
        $critical_files = [
            ABSPATH . \'wp-config.php\',
            ABSPATH . \'wp-login.php\',
            ABSPATH . \'index.php\'
        ];

        foreach ($critical_files as $file) {
            if (!file_exists($file)) {
                $this->alert("Critical file missing: $file");
            }
        }
    }

    private function check_new_admin_users() {
        $recent_admins = get_users(array(
            \'role\' => \'administrator\',
            \'date_query\' => array(
                array(\'after\' => \'24 hours ago\')
            )
        ));

        if (!empty($recent_admins)) {
            foreach ($recent_admins as $admin) {
                $this->alert("New admin user created: {$admin->user_login}");
            }
        }
    }

    private function check_failed_logins() {
        $attempts = get_option(\'failed_login_tracking\', []);
        $recent_attempts = 0;
        $current_time = time();

        foreach ($attempts as $data) {
            if ($current_time - $data[\'last_attempt\'] < 86400) { // Last 24 hours
                $recent_attempts += $data[\'count\'];
            }
        }

        if ($recent_attempts > 20) {
            $this->alert("High number of failed login attempts in last 24 hours: $recent_attempts");
        }
    }

    private function full_malware_scan() {
        $suspicious_patterns = [\'eval(\', \'base64_decode(\', \'gzinflate(\', \'system(\'];
        $scan_dirs = [
            ABSPATH . \'wp-content/themes/\',
            ABSPATH . \'wp-content/plugins/\',
            ABSPATH . \'wp-content/uploads/\'
        ];

        foreach ($scan_dirs as $dir) {
            if (is_dir($dir)) {
                $this->scan_directory($dir, $suspicious_patterns);
            }
        }
    }

    private function scan_directory($dir, $patterns) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === \'php\') {
                $content = file_get_contents($file->getPathname());

                foreach ($patterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        $this->alert("Suspicious pattern found in: " . $file->getPathname());
                        break;
                    }
                }
            }
        }
    }

    private function database_integrity_check() {
        global $wpdb;

        // Check for suspicious options
        $suspicious_options = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->options}
            WHERE option_value LIKE \'%base64_decode%\'
            OR option_value LIKE \'%eval(%\'
        ");

        if ($suspicious_options > 0) {
            $this->alert("Suspicious database entries found: $suspicious_options");
        }
    }

    private function update_check() {
        wp_update_plugins();
        wp_update_themes();

        $plugin_updates = get_site_transient(\'update_plugins\');
        $theme_updates = get_site_transient(\'update_themes\');

        if (!empty($plugin_updates->response)) {
            $this->alert("Plugin updates available: " . count($plugin_updates->response));
        }

        if (!empty($theme_updates->response)) {
            $this->alert("Theme updates available: " . count($theme_updates->response));
        }
    }

    private function alert($message) {
        error_log("SECURITY SCAN ALERT: " . $message);

        $admin_email = get_option(\'admin_email\');
        if ($admin_email) {
            wp_mail($admin_email, \'Security Scan Alert\', $message);
        }
    }

    private function log_scan($message) {
        error_log("SECURITY SCAN: " . $message);
    }
}

new AutomatedScanner();
';

        if (!file_exists($scanner_file)) {
            file_put_contents($scanner_file, $scanner_code);
            $this->log("Created automated security scanner");
        }
    }

    /**
     * Scan for malicious files
     */
    private function scanForMaliciousFiles()
    {
        $this->log("Scanning for malicious files...");
        $suspicious_files = [];

        // Scan wp-content directory
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(ABSPATH . 'wp-content')
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());

                // Check for malicious patterns
                foreach ($this->malicious_patterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        // Additional checks to avoid false positives
                        if ($this->isSuspiciousFile($file->getPathname(), $content)) {
                            $suspicious_files[] = $file->getPathname();
                            $this->log("SUSPICIOUS FILE: " . $file->getPathname());
                        }
                    }
                }
            }
        }

        return $suspicious_files;
    }

    /**
     * Check if file is truly suspicious
     */
    private function isSuspiciousFile($filepath, $content)
    {
        // Skip legitimate plugin files
        if (strpos($filepath, 'wp-content/plugins') !== false) {
            // Check if it's a known legitimate plugin
            $legitimate_plugins = ['wpforms', 'woocommerce', 'wordfence'];
            foreach ($legitimate_plugins as $plugin) {
                if (strpos($filepath, $plugin) !== false) {
                    return false;
                }
            }
        }

        // Check for heavily obfuscated code
        if (preg_match('/[a-zA-Z0-9+\/]{100,}/', $content)) {
            return true;
        }

        // Check for suspicious file names
        $suspicious_names = ['wp-weo.php', 'wp-wep.php', 'wp-weq.php', 'index.php'];
        $filename = basename($filepath);

        if (in_array($filename, $suspicious_names) && strpos($filepath, 'wp-content/uploads') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Set proper file permissions
     */
    private function setFilePermissions()
    {
        $this->log("Setting proper file permissions...");

        // WordPress recommended permissions
        $permissions = [
            ABSPATH => 0755,
            ABSPATH . 'wp-config.php' => 0600,
            ABSPATH . 'wp-content' => 0755,
            ABSPATH . 'wp-content/themes' => 0755,
            ABSPATH . 'wp-content/plugins' => 0755,
            ABSPATH . 'wp-content/uploads' => 0755,
        ];

        foreach ($permissions as $path => $permission) {
            if (file_exists($path)) {
                chmod($path, $permission);
                $this->log("Set permissions for: $path to " . decoct($permission));
            }
        }

        // Set file permissions recursively
        $this->setRecursivePermissions(ABSPATH . 'wp-content', 0644, 0755);
    }

    /**
     * Set recursive permissions
     */
    private function setRecursivePermissions($path, $file_perm, $dir_perm)
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($path)
        );

        foreach ($iterator as $item) {
            if ($item->isFile()) {
                chmod($item->getPathname(), $file_perm);
            } elseif ($item->isDir()) {
                chmod($item->getPathname(), $dir_perm);
            }
        }
    }

    /**
     * Secure wp-config.php
     */
    private function secureWpConfig()
    {
        $this->log("Securing wp-config.php...");

        $wp_config_path = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_path)) {
            $this->log("wp-config.php not found!");
            return;
        }

        $content = file_get_contents($wp_config_path);

        // Add security constants if not present
        $security_constants = [
            "define('DISALLOW_FILE_EDIT', true);",
            "define('DISALLOW_FILE_MODS', true);",
            "define('FORCE_SSL_ADMIN', true);",
            "define('WP_DEBUG', false);",
            "define('WP_DEBUG_LOG', false);",
            "define('WP_DEBUG_DISPLAY', false);"
        ];

        $modified = false;
        foreach ($security_constants as $constant) {
            if (strpos($content, $constant) === false) {
                $content = str_replace("<?php", "<?php\n" . $constant, $content);
                $modified = true;
                $this->log("Added security constant: $constant");
            }
        }

        if ($modified) {
            file_put_contents($wp_config_path, $content);
            $this->log("wp-config.php updated with security constants");
        }
    }

    /**
     * Update .htaccess with security rules
     */
    private function updateHtaccess()
    {
        $this->log("Updating .htaccess security rules...");

        $htaccess_path = ABSPATH . '.htaccess';
        $security_rules = "
# Security Rules - Added by Security Hardening Script
<Files wp-config.php>
order allow,deny
deny from all
</Files>

# Prevent access to PHP files in uploads
<Directory wp-content/uploads>
    <Files *.php>
        Order Deny,Allow
        Deny from all
    </Files>
</Directory>

# Block suspicious requests
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteCond %{QUERY_STRING} (eval\() [NC,OR]
RewriteCond %{QUERY_STRING} (base64_decode\() [NC,OR]
RewriteCond %{QUERY_STRING} (gzinflate\() [NC,OR]
RewriteCond %{QUERY_STRING} (system\() [NC]
RewriteRule .* - [F,L]
</IfModule>

# Limit file upload size
LimitRequestBody 10485760

";

        if (file_exists($htaccess_path)) {
            $current_content = file_get_contents($htaccess_path);
            if (strpos($current_content, '# Security Rules - Added by Security Hardening Script') === false) {
                file_put_contents($htaccess_path, $security_rules . $current_content);
                $this->log(".htaccess updated with security rules");
            }
        } else {
            file_put_contents($htaccess_path, $security_rules);
            $this->log(".htaccess created with security rules");
        }
    }

    /**
     * Clean uploads directory
     */
    private function cleanUploadsDirectory()
    {
        $this->log("Cleaning uploads directory...");

        $uploads_dir = ABSPATH . 'wp-content/uploads';
        $suspicious_files = [];

        // Find PHP files in uploads (should not exist)
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($uploads_dir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                // Skip legitimate font data files
                if (strpos($file->getPathname(), 'woocommerce-pdf-invoices') === false) {
                    $suspicious_files[] = $file->getPathname();
                    $this->log("SUSPICIOUS UPLOAD: " . $file->getPathname());
                }
            }
        }

        return $suspicious_files;
    }

    /**
     * Generate security report
     */
    private function generateSecurityReport()
    {
        $this->log("=== SECURITY REPORT ===");
        $this->log("Scan completed at: " . date('Y-m-d H:i:s'));
        $this->log("WordPress version: " . get_bloginfo('version'));
        $this->log("PHP version: " . phpversion());

        // Check for security plugins
        $security_plugins = ['wordfence', 'sucuri', 'ithemes-security'];
        foreach ($security_plugins as $plugin) {
            if (is_dir(ABSPATH . "wp-content/plugins/$plugin")) {
                $this->log("Security plugin found: $plugin");
            }
        }

        $this->log("=== END SECURITY REPORT ===");
    }

    /**
     * Log messages
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message\n";
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        echo $log_entry;
    }
}

// Run the security hardening if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $security = new WordPressSecurity();
    $security->hardenSecurity();
}
