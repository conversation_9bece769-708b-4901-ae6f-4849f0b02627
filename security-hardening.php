<?php
/**
 * WordPress Security Hardening Script
 * This script implements comprehensive security measures to protect against attacks
 * 
 * IMPORTANT: Run this script with caution and backup your site first!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

class WordPressSecurity {
    
    private $log_file;
    private $malicious_patterns = [
        'eval(',
        'base64_decode(',
        'gzinflate(',
        'str_rot13(',
        'gzuncompress(',
        'system(',
        'exec(',
        'shell_exec(',
        'passthru(',
        'file_get_contents(',
        'curl_exec(',
        'fopen(',
        'fwrite(',
        'file_put_contents('
    ];
    
    public function __construct() {
        $this->log_file = ABSPATH . 'security-scan.log';
        $this->log("=== WordPress Security Hardening Started ===");
    }
    
    /**
     * Main security hardening function
     */
    public function hardenSecurity() {
        echo "Starting WordPress Security Hardening...\n";
        
        // 1. Scan for malicious files
        $this->scanForMaliciousFiles();
        
        // 2. Set proper file permissions
        $this->setFilePermissions();
        
        // 3. Secure wp-config.php
        $this->secureWpConfig();
        
        // 4. Create/update .htaccess security rules
        $this->updateHtaccess();
        
        // 5. Remove suspicious uploads
        $this->cleanUploadsDirectory();
        
        // 6. Generate security report
        $this->generateSecurityReport();
        
        echo "Security hardening completed. Check security-scan.log for details.\n";
    }
    
    /**
     * Scan for malicious files
     */
    private function scanForMaliciousFiles() {
        $this->log("Scanning for malicious files...");
        $suspicious_files = [];
        
        // Scan wp-content directory
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator(ABSPATH . 'wp-content')
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                
                // Check for malicious patterns
                foreach ($this->malicious_patterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        // Additional checks to avoid false positives
                        if ($this->isSuspiciousFile($file->getPathname(), $content)) {
                            $suspicious_files[] = $file->getPathname();
                            $this->log("SUSPICIOUS FILE: " . $file->getPathname());
                        }
                    }
                }
            }
        }
        
        return $suspicious_files;
    }
    
    /**
     * Check if file is truly suspicious
     */
    private function isSuspiciousFile($filepath, $content) {
        // Skip legitimate plugin files
        if (strpos($filepath, 'wp-content/plugins') !== false) {
            // Check if it's a known legitimate plugin
            $legitimate_plugins = ['wpforms', 'woocommerce', 'wordfence'];
            foreach ($legitimate_plugins as $plugin) {
                if (strpos($filepath, $plugin) !== false) {
                    return false;
                }
            }
        }
        
        // Check for heavily obfuscated code
        if (preg_match('/[a-zA-Z0-9+\/]{100,}/', $content)) {
            return true;
        }
        
        // Check for suspicious file names
        $suspicious_names = ['wp-weo.php', 'wp-wep.php', 'wp-weq.php', 'index.php'];
        $filename = basename($filepath);
        
        if (in_array($filename, $suspicious_names) && strpos($filepath, 'wp-content/uploads') !== false) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Set proper file permissions
     */
    private function setFilePermissions() {
        $this->log("Setting proper file permissions...");
        
        // WordPress recommended permissions
        $permissions = [
            ABSPATH => 0755,
            ABSPATH . 'wp-config.php' => 0600,
            ABSPATH . 'wp-content' => 0755,
            ABSPATH . 'wp-content/themes' => 0755,
            ABSPATH . 'wp-content/plugins' => 0755,
            ABSPATH . 'wp-content/uploads' => 0755,
        ];
        
        foreach ($permissions as $path => $permission) {
            if (file_exists($path)) {
                chmod($path, $permission);
                $this->log("Set permissions for: $path to " . decoct($permission));
            }
        }
        
        // Set file permissions recursively
        $this->setRecursivePermissions(ABSPATH . 'wp-content', 0644, 0755);
    }
    
    /**
     * Set recursive permissions
     */
    private function setRecursivePermissions($path, $file_perm, $dir_perm) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($path)
        );
        
        foreach ($iterator as $item) {
            if ($item->isFile()) {
                chmod($item->getPathname(), $file_perm);
            } elseif ($item->isDir()) {
                chmod($item->getPathname(), $dir_perm);
            }
        }
    }
    
    /**
     * Secure wp-config.php
     */
    private function secureWpConfig() {
        $this->log("Securing wp-config.php...");
        
        $wp_config_path = ABSPATH . 'wp-config.php';
        if (!file_exists($wp_config_path)) {
            $this->log("wp-config.php not found!");
            return;
        }
        
        $content = file_get_contents($wp_config_path);
        
        // Add security constants if not present
        $security_constants = [
            "define('DISALLOW_FILE_EDIT', true);",
            "define('DISALLOW_FILE_MODS', true);",
            "define('FORCE_SSL_ADMIN', true);",
            "define('WP_DEBUG', false);",
            "define('WP_DEBUG_LOG', false);",
            "define('WP_DEBUG_DISPLAY', false);"
        ];
        
        $modified = false;
        foreach ($security_constants as $constant) {
            if (strpos($content, $constant) === false) {
                $content = str_replace("<?php", "<?php\n" . $constant, $content);
                $modified = true;
                $this->log("Added security constant: $constant");
            }
        }
        
        if ($modified) {
            file_put_contents($wp_config_path, $content);
            $this->log("wp-config.php updated with security constants");
        }
    }
    
    /**
     * Update .htaccess with security rules
     */
    private function updateHtaccess() {
        $this->log("Updating .htaccess security rules...");
        
        $htaccess_path = ABSPATH . '.htaccess';
        $security_rules = "
# Security Rules - Added by Security Hardening Script
<Files wp-config.php>
order allow,deny
deny from all
</Files>

# Prevent access to PHP files in uploads
<Directory wp-content/uploads>
    <Files *.php>
        Order Deny,Allow
        Deny from all
    </Files>
</Directory>

# Block suspicious requests
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteCond %{QUERY_STRING} (eval\() [NC,OR]
RewriteCond %{QUERY_STRING} (base64_decode\() [NC,OR]
RewriteCond %{QUERY_STRING} (gzinflate\() [NC,OR]
RewriteCond %{QUERY_STRING} (system\() [NC]
RewriteRule .* - [F,L]
</IfModule>

# Limit file upload size
LimitRequestBody 10485760

";
        
        if (file_exists($htaccess_path)) {
            $current_content = file_get_contents($htaccess_path);
            if (strpos($current_content, '# Security Rules - Added by Security Hardening Script') === false) {
                file_put_contents($htaccess_path, $security_rules . $current_content);
                $this->log(".htaccess updated with security rules");
            }
        } else {
            file_put_contents($htaccess_path, $security_rules);
            $this->log(".htaccess created with security rules");
        }
    }
    
    /**
     * Clean uploads directory
     */
    private function cleanUploadsDirectory() {
        $this->log("Cleaning uploads directory...");
        
        $uploads_dir = ABSPATH . 'wp-content/uploads';
        $suspicious_files = [];
        
        // Find PHP files in uploads (should not exist)
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($uploads_dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                // Skip legitimate font data files
                if (strpos($file->getPathname(), 'woocommerce-pdf-invoices') === false) {
                    $suspicious_files[] = $file->getPathname();
                    $this->log("SUSPICIOUS UPLOAD: " . $file->getPathname());
                }
            }
        }
        
        return $suspicious_files;
    }
    
    /**
     * Generate security report
     */
    private function generateSecurityReport() {
        $this->log("=== SECURITY REPORT ===");
        $this->log("Scan completed at: " . date('Y-m-d H:i:s'));
        $this->log("WordPress version: " . get_bloginfo('version'));
        $this->log("PHP version: " . phpversion());
        
        // Check for security plugins
        $security_plugins = ['wordfence', 'sucuri', 'ithemes-security'];
        foreach ($security_plugins as $plugin) {
            if (is_dir(ABSPATH . "wp-content/plugins/$plugin")) {
                $this->log("Security plugin found: $plugin");
            }
        }
        
        $this->log("=== END SECURITY REPORT ===");
    }
    
    /**
     * Log messages
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message\n";
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        echo $log_entry;
    }
}

// Run the security hardening if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $security = new WordPressSecurity();
    $security->hardenSecurity();
}
?>
